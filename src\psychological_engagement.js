/**
 * Advanced Psychological Engagement System
 * Implements human-touch communication and persuasive conversation strategies
 */

class PsychologicalEngagement {
    constructor() {
        // Emotional state indicators
        this.EMOTIONAL_INDICATORS = {
            frustrated: ['frustrated', 'annoying', 'tired', 'fed up', 'sick of', 'hate', 'difficult', 'hard'],
            excited: ['excited', 'amazing', 'great', 'awesome', 'fantastic', 'love', 'perfect'],
            worried: ['worried', 'concerned', 'afraid', 'scared', 'nervous', 'anxious', 'unsure'],
            skeptical: ['doubt', 'skeptical', 'not sure', 'maybe', 'possibly', 'might', 'uncertain'],
            urgent: ['urgent', 'asap', 'quickly', 'fast', 'immediately', 'now', 'soon', 'deadline']
        };

        // Psychological techniques
        this.TECHNIQUES = {
            FEEL_FELT_FOUND: 'feel_felt_found',
            YES_LADDER: 'yes_ladder',
            SCARCITY: 'scarcity',
            SOCIAL_PROOF: 'social_proof',
            RECIPROCITY: 'reciprocity',
            AUTHORITY: 'authority',
            COMMITMENT: 'commitment'
        };

        // Objection patterns and responses
        this.OBJECTION_PATTERNS = {
            price: {
                keywords: ['expensive', 'cost', 'price', 'budget', 'money', 'afford', 'cheap'],
                responses: [
                    "I understand budget considerations are crucial. Let me share how I approach ROI analysis for projects like this.",
                    "Investment planning is important. Based on my experience, what metrics would be most valuable for your decision?",
                    "Budget optimization is part of my expertise. What's your target ROI timeframe for this type of solution?"
                ]
            },
            time: {
                keywords: ['time', 'busy', 'later', 'not now', 'schedule', 'when'],
                responses: [
                    "I completely understand time constraints. In my experience, the right system actually creates more time.",
                    "Time efficiency is exactly what I specialize in. What processes take up most of your time currently?",
                    "Timing is important for implementation success. What would be an ideal timeline from your perspective?"
                ]
            },
            trust: {
                keywords: ['trust', 'sure', 'guarantee', 'proof', 'evidence', 'track record'],
                responses: [
                    "Due diligence is smart. My portfolio at renatahenessa.com shows measurable results from previous projects.",
                    "I appreciate thorough evaluation. What specific outcomes would demonstrate success for your business?",
                    "Evidence-based decisions are wise. Would you like to see case studies with quantified results?"
                ]
            },
            authority: {
                keywords: ['decide', 'boss', 'partner', 'team', 'discuss', 'think about'],
                responses: [
                    "Collaborative decision-making is important. What information would be helpful for your team's evaluation?",
                    "Team alignment is crucial for project success. What are the key concerns they typically have?",
                    "I can prepare a technical brief that addresses common stakeholder questions. Would that be helpful?"
                ]
            }
        };

        // Professional credibility examples
        this.SOCIAL_PROOF = [
            "In my recent startup project, we achieved 100% operational cost reduction through system optimization",
            "My data science background helps me deliver measurable results - like 88% process time reduction",
            "With my BSc in Data Science and 89.89/100 certification, I focus on evidence-based solutions",
            "Previous clients have seen 60% efficiency improvements through my systematic approach",
            "My portfolio at renatahenessa.com shows quantified results from multiple successful projects"
        ];

        // Professional availability and timing
        this.SCARCITY_TRIGGERS = [
            "My consultation calendar is filling up for this month",
            "I typically plan project timelines 2-3 weeks in advance",
            "My development schedule has some availability in the coming weeks",
            "I prefer to take on projects where I can dedicate proper attention",
            "Planning ahead ensures better project outcomes and timeline management"
        ];

        // Yes ladder questions (easy to agree with)
        this.YES_LADDER_QUESTIONS = [
            "You want your business to grow, right?",
            "Saving time and money sounds good, doesn't it?",
            "You'd like to stay ahead of your competition, wouldn't you?",
            "Having better data to make decisions would be valuable, correct?",
            "You want systems that actually work for your business, right?"
        ];
    }

    /**
     * Detect emotional state from message
     */
    detectEmotionalState(message) {
        const lowerMessage = message.toLowerCase();
        
        for (const [emotion, indicators] of Object.entries(this.EMOTIONAL_INDICATORS)) {
            if (indicators.some(indicator => lowerMessage.includes(indicator))) {
                return emotion;
            }
        }
        
        return 'neutral';
    }

    /**
     * Generate empathetic response using Feel-Felt-Found technique
     */
    generateFeelFeltFound(emotion, context) {
        const templates = {
            frustrated: {
                feel: "I can feel your frustration with this situation",
                felt: "Many business owners have felt exactly the same way",
                found: "What they found was that the right system completely transformed their operations"
            },
            worried: {
                feel: "I understand your concerns about making the right choice",
                felt: "Other clients felt the same uncertainty before starting",
                found: "What they found was that taking action was the best decision they made"
            },
            skeptical: {
                feel: "I appreciate your careful approach to this",
                felt: "Smart business owners have felt similar doubts",
                found: "What they found was that our track record speaks for itself"
            },
            excited: {
                feel: "I love your enthusiasm about growing your business",
                felt: "Other successful entrepreneurs have felt this same excitement",
                found: "What they found was that acting quickly gave them a competitive advantage"
            }
        };

        const template = templates[emotion] || templates.worried;
        return `${template.feel}. ${template.felt}, and ${template.found}.`;
    }

    /**
     * Detect objection type and generate response
     */
    handleObjection(message) {
        const lowerMessage = message.toLowerCase();
        
        for (const [objectionType, data] of Object.entries(this.OBJECTION_PATTERNS)) {
            if (data.keywords.some(keyword => lowerMessage.includes(keyword))) {
                const randomResponse = data.responses[Math.floor(Math.random() * data.responses.length)];
                return {
                    type: objectionType,
                    response: randomResponse,
                    followUp: this.getObjectionFollowUp(objectionType)
                };
            }
        }
        
        return null;
    }

    /**
     * Get follow-up question for objection handling
     */
    getObjectionFollowUp(objectionType) {
        const followUps = {
            price: "What budget range were you thinking for solving this challenge?",
            time: "If I could show you how to save 10 hours a week, would that change things?",
            trust: "What would you need to see to feel completely confident?",
            authority: "Should we schedule a call with your team to discuss this together?"
        };
        
        return followUps[objectionType] || "What's the main thing holding you back right now?";
    }

    /**
     * Generate social proof statement
     */
    getSocialProof(context = null) {
        return this.SOCIAL_PROOF[Math.floor(Math.random() * this.SOCIAL_PROOF.length)];
    }

    /**
     * Generate scarcity/urgency statement
     */
    getScarcityTrigger() {
        return this.SCARCITY_TRIGGERS[Math.floor(Math.random() * this.SCARCITY_TRIGGERS.length)];
    }

    /**
     * Get yes ladder question
     */
    getYesLadderQuestion() {
        return this.YES_LADDER_QUESTIONS[Math.floor(Math.random() * this.YES_LADDER_QUESTIONS.length)];
    }

    /**
     * Generate alternative close
     */
    generateAlternativeClose(context) {
        const alternatives = [
            "Would you prefer a 30-minute or 60-minute consultation to discuss your needs?",
            "Should we start with the basic system or the full implementation?",
            "Would this week or next week work better for a detailed discussion?",
            "Do you want to focus on the efficiency gains or the revenue growth potential first?",
            "Would you like to see the technical demo or the business case presentation?"
        ];
        
        return alternatives[Math.floor(Math.random() * alternatives.length)];
    }

    /**
     * Generate assumptive close
     */
    generateAssumptiveClose() {
        const assumptives = [
            "When would be the best time for Renata to call you this week?",
            "I'll have Renata prepare a custom proposal for your business. What's your email?",
            "Let me check Renata's calendar for the best consultation slot for you.",
            "I'll set up a priority consultation for you. What's your preferred time?",
            "Renata will want to understand your specific needs. When can she reach you?"
        ];
        
        return assumptives[Math.floor(Math.random() * assumptives.length)];
    }

    /**
     * Create open loop to maintain engagement
     */
    createOpenLoop() {
        const openLoops = [
            "There's something specific I want to show you about this solution...",
            "I have an idea that could be perfect for your situation, but first...",
            "This reminds me of a client who had the exact same challenge...",
            "There's a strategy that's working incredibly well right now...",
            "I'm curious about one thing regarding your business..."
        ];
        
        return openLoops[Math.floor(Math.random() * openLoops.length)];
    }

    /**
     * Generate re-engagement message for silent clients
     */
    generateReEngagement(daysSilent) {
        if (daysSilent <= 1) {
            return "Just wanted to make sure you got my last message. Any thoughts on what we discussed?";
        } else if (daysSilent <= 3) {
            return "I know you're probably busy, but I had another idea for your business that might interest you...";
        } else if (daysSilent <= 7) {
            return "Quick question - what's the biggest challenge you're facing in your business right now?";
        } else {
            return "I came across something that reminded me of our conversation. Mind if I share a quick insight?";
        }
    }

    /**
     * Analyze conversation for psychological insights
     */
    analyzeConversation(messages) {
        const analysis = {
            emotionalJourney: [],
            objectionPatterns: [],
            engagementLevel: 0,
            conversionReadiness: 0,
            recommendedTechnique: null
        };

        messages.forEach(msg => {
            if (msg.isFromUser) {
                const emotion = this.detectEmotionalState(msg.message);
                analysis.emotionalJourney.push(emotion);
                
                const objection = this.handleObjection(msg.message);
                if (objection) {
                    analysis.objectionPatterns.push(objection.type);
                }
            }
        });

        // Calculate engagement and readiness scores
        analysis.engagementLevel = this.calculateEngagementLevel(messages);
        analysis.conversionReadiness = this.calculateConversionReadiness(analysis);
        analysis.recommendedTechnique = this.recommendTechnique(analysis);

        return analysis;
    }

    /**
     * Calculate engagement level based on message patterns
     */
    calculateEngagementLevel(messages) {
        const userMessages = messages.filter(m => m.isFromUser);
        const avgLength = userMessages.reduce((sum, m) => sum + m.message.length, 0) / userMessages.length;
        const responseTime = this.calculateAverageResponseTime(messages);
        
        let score = 0;
        if (avgLength > 50) score += 30; // Detailed responses
        if (userMessages.length > 5) score += 25; // Multiple interactions
        if (responseTime < 300) score += 25; // Quick responses (5 min)
        if (this.hasQuestions(userMessages)) score += 20; // Asking questions
        
        return Math.min(100, score);
    }

    /**
     * Calculate conversion readiness score
     */
    calculateConversionReadiness(analysis) {
        let score = 0;
        
        // Positive emotional indicators
        if (analysis.emotionalJourney.includes('excited')) score += 30;
        if (analysis.emotionalJourney.includes('urgent')) score += 25;
        
        // Engagement level contribution
        score += analysis.engagementLevel * 0.3;
        
        // Objection handling (shows serious consideration)
        if (analysis.objectionPatterns.length > 0) score += 20;
        
        return Math.min(100, score);
    }

    /**
     * Recommend best psychological technique for current situation
     */
    recommendTechnique(analysis) {
        if (analysis.conversionReadiness > 70) return this.TECHNIQUES.COMMITMENT;
        if (analysis.objectionPatterns.length > 0) return this.TECHNIQUES.FEEL_FELT_FOUND;
        if (analysis.emotionalJourney.includes('skeptical')) return this.TECHNIQUES.SOCIAL_PROOF;
        if (analysis.emotionalJourney.includes('worried')) return this.TECHNIQUES.AUTHORITY;
        if (analysis.engagementLevel < 50) return this.TECHNIQUES.YES_LADDER;
        
        return this.TECHNIQUES.SCARCITY;
    }

    /**
     * Helper methods
     */
    calculateAverageResponseTime(messages) {
        // Simplified - in real implementation, calculate actual time differences
        return 180; // 3 minutes average
    }

    hasQuestions(messages) {
        return messages.some(m => m.message.includes('?'));
    }
}

module.exports = PsychologicalEngagement;
