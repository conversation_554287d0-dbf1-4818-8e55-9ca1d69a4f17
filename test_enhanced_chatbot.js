const { runAiAssistant, conversationManager } = require('./src/ai_assistant');
const AnalyticsManager = require('./src/analytics_manager');

/**
 * Test script for the enhanced WhatsApp chatbot
 */
async function testEnhancedChatbot() {
    console.log('🚀 Testing Enhanced WhatsApp Chatbot Features...\n');

    // Test contact information
    const testContactId = '<EMAIL>';
    const testContactName = 'Test User';

    try {
        console.log('1. Testing Initial Conversation Flow...');
        
        // Initial message
        let response = await runAiAssistant('Halo, saya butuh bantuan untuk bisnis saya', testContactId, testContactName);
        console.log('✅ Initial Response:', response.substring(0, 100) + '...\n');

        // Business qualification
        response = await runAiAssistant('Saya punya toko online tapi sales menurun', testContactId, testContactName);
        console.log('✅ Qualification Response:', response.substring(0, 100) + '...\n');

        // Problem discovery
        response = await runAiAssistant('Iya, masalahnya website saya lambat dan susah digunakan', testContactId, testContactName);
        console.log('✅ Problem Discovery Response:', response.substring(0, 100) + '...\n');

        // Solution presentation
        response = await runAiAssistant('Dampaknya besar, kehilangan banyak customer', testContactId, testContactName);
        console.log('✅ Solution Presentation Response:', response.substring(0, 100) + '...\n');

        // Interest building
        response = await runAiAssistant('Menarik, bagaimana cara kerjanya?', testContactId, testContactName);
        console.log('✅ Interest Building Response:', response.substring(0, 100) + '...\n');

        // Buying intent
        response = await runAiAssistant('Sounds good, saya interested. Let\'s proceed!', testContactId, testContactName);
        console.log('✅ Deal Closure Response:', response.substring(0, 100) + '...\n');

        console.log('2. Testing Conversation Manager...');
        const conversation = conversationManager.getConversation(testContactId);
        console.log('✅ Conversation Stage:', conversation.stage);
        console.log('✅ Is Converted:', conversation.isConverted);
        console.log('✅ Handoff Triggered:', conversation.handoffTriggered);
        console.log('✅ Pain Points:', conversation.painPoints);
        console.log('✅ Problem Category:', conversation.problemCategory);
        console.log('✅ Conversion Probability:', conversation.conversionProbability + '%\n');

        console.log('3. Testing Analytics Manager...');
        const analyticsManager = new AnalyticsManager();
        const report = analyticsManager.generateReport();
        
        console.log('✅ Analytics Overview:');
        console.log('   - Total Conversations:', report.overview.totalConversations);
        console.log('   - Converted Conversations:', report.overview.convertedConversations);
        console.log('   - Conversion Rate:', report.overview.conversionRate + '%');
        console.log('   - Avg Messages per Conversation:', report.overview.avgMessagesPerConversation);
        
        console.log('\n✅ Conversion Funnel:');
        Object.entries(report.conversionFunnel.funnel).forEach(([stage, count]) => {
            console.log(`   - ${stage}: ${count}`);
        });

        console.log('\n✅ Top Pain Points:');
        report.painPointAnalysis.topPainPoints.slice(0, 3).forEach(([point, freq]) => {
            console.log(`   - ${point.substring(0, 50)}... (${freq}x)`);
        });

        console.log('\n✅ Recommendations:');
        report.recommendations.forEach(rec => {
            console.log(`   - ${rec.message}`);
        });

        console.log('\n4. Testing CSV Export...');
        const csvData = analyticsManager.exportAnalyticsToCSV();
        const conversationCsvData = conversationManager.exportToCSV();
        
        console.log('✅ Analytics CSV Length:', csvData.length, 'characters');
        console.log('✅ Conversations CSV Length:', conversationCsvData.length, 'characters');

        console.log('\n5. Testing Portfolio Integration...');
        response = await runAiAssistant('Bisa lihat portfolio Anda?', testContactId + '_2', 'Test User 2');
        const hasPortfolioInfo = response.includes('renatahenessa.com') || 
                                response.includes('sistem bisnis') || 
                                response.includes('warehouse management');
        console.log('✅ Portfolio Integration:', hasPortfolioInfo ? 'PASSED' : 'FAILED');

        console.log('\n6. Testing Content Filtering...');
        response = await runAiAssistant('Bagaimana pendapat Anda tentang politik?', testContactId + '_3', 'Test User 3');
        const hasContentFilter = response.includes('konsultasi bisnis') || 
                                response.includes('fokus membantu');
        console.log('✅ Content Filtering:', hasContentFilter ? 'PASSED' : 'FAILED');

        console.log('\n7. Testing Human-like Responses...');
        response = await runAiAssistant('Bisnis saya stuck, tidak tahu harus bagaimana', testContactId + '_4', 'Test User 4');
        const isHumanLike = !response.includes('sebagai AI') && 
                           !response.includes('saya adalah bot') &&
                           (response.includes('understand') || response.includes('pasti') || response.includes('kebayang'));
        console.log('✅ Human-like Responses:', isHumanLike ? 'PASSED' : 'FAILED');

        console.log('\n🎉 All tests completed successfully!');
        console.log('\n📊 Final Statistics:');
        const finalStats = conversationManager.getStatistics();
        console.log('   - Total Conversations:', finalStats.total);
        console.log('   - Converted:', finalStats.converted);
        console.log('   - Conversion Rate:', finalStats.conversionRate + '%');
        console.log('   - Average Conversion Time:', finalStats.avgConversionTime, 'minutes');

    } catch (error) {
        console.error('❌ Test failed:', error);
    }
}

/**
 * Test specific conversation scenarios
 */
async function testConversationScenarios() {
    console.log('\n🎭 Testing Conversation Scenarios...\n');

    const scenarios = [
        {
            name: 'E-commerce Business',
            messages: [
                'Halo, saya punya toko online',
                'Masalahnya conversion rate rendah',
                'Iya, banyak visitor tapi sedikit yang beli',
                'Menarik, bisa bantu tingkatkan conversion?'
            ]
        },
        {
            name: 'Manufacturing Business',
            messages: [
                'Saya butuh sistem untuk pabrik',
                'Inventory management masih manual',
                'Sering terjadi stock out dan overstock',
                'Berapa lama implementasinya?'
            ]
        },
        {
            name: 'Service Business',
            messages: [
                'Bisnis jasa saya perlu digitalisasi',
                'Susah track customer dan project',
                'Sering miss deadline karena koordinasi buruk',
                'Sounds like exactly what I need!'
            ]
        }
    ];

    for (const scenario of scenarios) {
        console.log(`Testing ${scenario.name}...`);
        const contactId = `test_${scenario.name.toLowerCase().replace(' ', '_')}@c.us`;
        
        for (let i = 0; i < scenario.messages.length; i++) {
            const response = await runAiAssistant(scenario.messages[i], contactId, scenario.name);
            console.log(`  Message ${i + 1}: ${response.substring(0, 80)}...`);
        }
        
        const conversation = conversationManager.getConversation(contactId);
        console.log(`  Final Stage: ${conversation.stage}`);
        console.log(`  Conversion Probability: ${conversation.conversionProbability}%`);
        console.log(`  Pain Points: ${conversation.painPoints.length}\n`);
    }
}

// Run tests
if (require.main === module) {
    testEnhancedChatbot()
        .then(() => testConversationScenarios())
        .then(() => {
            console.log('\n✅ All tests completed successfully!');
            process.exit(0);
        })
        .catch(error => {
            console.error('❌ Tests failed:', error);
            process.exit(1);
        });
}

module.exports = {
    testEnhancedChatbot,
    testConversationScenarios
};
