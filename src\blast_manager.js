const BusinessDataManager = require('./business_data');
const LeadManager = require('./lead_manager');

class BlastManager {
    constructor(whatsappClient) {
        this.client = whatsappClient;
        this.businessDataManager = new BusinessDataManager();
        this.leadManager = new LeadManager();
        this.isBlasting = false;
        this.blastStats = {
            total: 0,
            sent: 0,
            failed: 0,
            startTime: null,
            endTime: null
        };
    }

    /**
     * Enhanced conversation starter templates with credibility and value proposition
     */
    getMessageTemplates() {
        return {
            web_development: `<PERSON>o {{business_name}},

Saya Renata, Web Developer & Data Scientist. Nemu bisnis Anda di Google Maps.

Gimana kabar websitenya? Udah banyak yang kontak lewat situ?

Kalau butuh upgrade atau bikin website baru yang lebih menarik customer, saya bisa bantu.

🌐 Portfolio: renatahenessa.com`,

            system_development: `Halo {{business_name}},

Saya Renata, Data Scientist & Web Developer. Ketemu bisnis Anda di Google Maps nih.

Lagi sibuk apa? Masih banyak kerjaan manual yang ribet?

Kalau butuh sistem otomatis atau dashboard analytics buat efisiensi operasional, saya ada solusinya.

🌐 Portfolio: renatahenessa.com`,

            complete_package: `Halo {{business_name}},

Saya Renata, Web Developer & Data Scientist. Nemu bisnis Anda di Google Maps.

Gimana kabar bisnisnya? Semoga lancar terus ya.

Btw, kalau butuh website, dashboard analytics, atau sistem digital buat ningkatin penjualan, saya bisa bantu.

🌐 Portfolio: renatahenessa.com`,

            digital_solutions: `Halo {{business_name}},

Saya Renata, Data Scientist & Web Developer. Saya lihat bisnis Anda di Google Maps.

Gimana perkembangannya?

Kalau lagi cari solusi digital buat boost bisnis - website, dashboard analytics, atau sistem manajemen - saya ada pengalaman di bidang itu.

🌐 Portfolio: renatahenessa.com`,

            business_growth: `Halo {{business_name}},

Saya Renata, Web Developer & Data Scientist. Ketemu kontak Anda di Google Maps.

Apa kabar bisnisnya?

Kalau butuh bantuan teknologi buat scale up - website profesional, dashboard analytics, atau sistem automasi - saya bisa sharing solusinya.

🌐 Portfolio: renatahenessa.com`,

            casual_tech_check: `Halo {{business_name}},

Saya Renata, Data Scientist & Web Developer. Nemu bisnis Anda di Google Maps.

Gimana kabarnya?

Btw, udah punya website atau dashboard analytics belum? Kalau butuh upgrade teknologi buat bisnis, saya bisa bantu.

🌐 Portfolio: renatahenessa.com`
        };
    }

    /**
     * Send WhatsApp blast to businesses
     */
    async sendBlast(templateName = 'digital_solutions', options = {}) {
        if (this.isBlasting) {
            throw new Error('Blast is already in progress');
        }

        try {
            this.isBlasting = true;
            this.blastStats = {
                total: 0,
                sent: 0,
                failed: 0,
                startTime: new Date(),
                endTime: null
            };

            const {
                batchSize = 10,
                delayBetweenMessages = 3000,
                delayBetweenBatches = 30000,
                maxBusinesses = null
            } = options;

            console.log('🚀 Starting WhatsApp business blast...');
            
            const templates = this.getMessageTemplates();
            const messageTemplate = templates[templateName] || templates.digital_solutions;
            
            let uncontactedBusinesses = this.businessDataManager.getUncontactedBusinesses();
            
            if (maxBusinesses) {
                uncontactedBusinesses = uncontactedBusinesses.slice(0, maxBusinesses);
            }
            
            this.blastStats.total = uncontactedBusinesses.length;
            
            console.log(`📊 Found ${uncontactedBusinesses.length} uncontacted businesses`);
            
            if (uncontactedBusinesses.length === 0) {
                console.log('❌ No uncontacted businesses found');
                this.isBlasting = false;
                return this.blastStats;
            }

            // Process businesses in batches
            for (let i = 0; i < uncontactedBusinesses.length; i += batchSize) {
                const batch = uncontactedBusinesses.slice(i, i + batchSize);
                const batchNumber = Math.floor(i/batchSize) + 1;
                const totalBatches = Math.ceil(uncontactedBusinesses.length/batchSize);
                
                console.log(`📦 Processing batch ${batchNumber}/${totalBatches} (${batch.length} businesses)`);
                
                for (const business of batch) {
                    try {
                        // Validate business data
                        if (!business.name || !business.phone) {
                            console.warn(`⚠️ Skipping business with incomplete data: ${JSON.stringify(business)}`);
                            this.blastStats.failed++;
                            continue;
                        }

                        // Clean and validate phone number
                        const cleanedPhone = this.businessDataManager.cleanPhoneNumber(business.phone);
                        if (!cleanedPhone) {
                            console.warn(`⚠️ Skipping business with invalid phone: ${business.name} (${business.phone})`);
                            this.blastStats.failed++;
                            continue;
                        }

                        // Personalize the message
                        const personalizedMessage = messageTemplate
                            .replace(/{{business_name}}/g, business.name);

                        const contactId = `${cleanedPhone}@c.us`;

                        console.log(`📞 Processing: ${business.name} (${business.phone} -> ${cleanedPhone})`);

                        // Send the message
                        const messageSent = await this.sendMessageSafely(contactId, personalizedMessage);

                        if (messageSent) {
                            // Mark as contacted
                            this.businessDataManager.markAsContacted(business.id, `blast_sent_${templateName}`);
                            this.blastStats.sent++;
                            console.log(`✅ [${this.blastStats.sent}/${this.blastStats.total}] ${business.name} (${cleanedPhone})`);
                        } else {
                            this.blastStats.failed++;
                            console.log(`❌ [${this.blastStats.failed} failed] ${business.name} (${cleanedPhone})`);
                        }

                        // Delay between messages
                        await this.delay(delayBetweenMessages);

                    } catch (error) {
                        this.blastStats.failed++;
                        console.error(`💥 Error sending to ${business.name}:`, error.message);
                    }
                }
                
                // Longer delay between batches
                if (i + batchSize < uncontactedBusinesses.length) {
                    console.log(`⏳ Waiting ${delayBetweenBatches/1000} seconds before next batch...`);
                    await this.delay(delayBetweenBatches);
                }
            }
            
            this.blastStats.endTime = new Date();
            const duration = Math.round((this.blastStats.endTime - this.blastStats.startTime) / 1000);
            
            console.log(`🎉 Blast completed in ${duration} seconds:`);
            console.log(`   ✅ Sent: ${this.blastStats.sent}`);
            console.log(`   ❌ Failed: ${this.blastStats.failed}`);
            console.log(`   📊 Success rate: ${((this.blastStats.sent / this.blastStats.total) * 100).toFixed(1)}%`);
            
            return this.blastStats;
            
        } catch (error) {
            console.error('💥 Error in sendBlast:', error);
            throw error;
        } finally {
            this.isBlasting = false;
        }
    }

    /**
     * Send message safely with error handling and multiple retry methods
     */
    async sendMessageSafely(contactId, messageText) {
        try {
            if (!this.client || !this.client.info) {
                console.error('WhatsApp client is not ready');
                return false;
            }

            const cleanMessage = String(messageText).trim();
            if (!cleanMessage) {
                console.error('Cannot send empty message');
                return false;
            }

            // Validate contactId format
            if (!contactId || typeof contactId !== 'string') {
                console.error('Invalid contactId provided');
                return false;
            }

            console.log(`📤 Attempting to send blast message to ${contactId}`);

            // Method 1: Direct client.sendMessage
            try {
                await this.client.sendMessage(contactId, cleanMessage);
                console.log(`✅ Message sent successfully to ${contactId}`);
                return true;
            } catch (sendError) {
                console.warn(`⚠️ Method 1 failed for ${contactId}: ${sendError.message}`);
            }

            // Method 2: Try getting chat first, then sending
            try {
                console.log(`🔄 Trying alternative method for ${contactId}...`);
                const chat = await this.client.getChatById(contactId);
                if (chat) {
                    await chat.sendMessage(cleanMessage);
                    console.log(`✅ Message sent via chat object to ${contactId}`);
                    return true;
                }
            } catch (chatError) {
                console.warn(`⚠️ Method 2 failed for ${contactId}: ${chatError.message}`);
            }

            // Method 3: Wait and retry with delay
            try {
                console.log(`⏳ Waiting 3 seconds before retry for ${contactId}...`);
                await this.delay(3000);
                await this.client.sendMessage(contactId, cleanMessage);
                console.log(`✅ Message sent after retry to ${contactId}`);
                return true;
            } catch (retryError) {
                console.warn(`⚠️ Method 3 failed for ${contactId}: ${retryError.message}`);
            }

            // Method 4: Try to check if contact exists and create if needed
            try {
                console.log(`🔍 Checking contact existence for ${contactId}...`);
                const contact = await this.client.getContactById(contactId);
                if (contact) {
                    await this.client.sendMessage(contactId, cleanMessage);
                    console.log(`✅ Message sent after contact check to ${contactId}`);
                    return true;
                }
            } catch (contactError) {
                console.warn(`⚠️ Method 4 failed for ${contactId}: ${contactError.message}`);
            }

            console.error(`❌ All methods failed for ${contactId}`);
            return false;

        } catch (error) {
            console.error(`💥 Unexpected error sending to ${contactId}:`, error.message);
            return false;
        }
    }

    /**
     * Delay utility
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Get blast statistics
     */
    getBlastStats() {
        return {
            ...this.blastStats,
            isBlasting: this.isBlasting,
            businessStats: this.businessDataManager.getStatistics(),
            leadStats: this.leadManager.getStatistics()
        };
    }

    /**
     * Stop current blast
     */
    stopBlast() {
        if (this.isBlasting) {
            console.log('🛑 Stopping blast...');
            this.isBlasting = false;
            return true;
        }
        return false;
    }

    /**
     * Test sending message to a specific phone number
     */
    async testSendToNumber(phoneNumber, message = "Test message from WhatsApp bot") {
        try {
            console.log(`🧪 Testing message send to: ${phoneNumber}`);

            // Clean the phone number
            const cleanedPhone = this.businessDataManager.cleanPhoneNumber(phoneNumber);
            console.log(`📞 Cleaned phone number: ${phoneNumber} -> ${cleanedPhone}`);

            const contactId = `${cleanedPhone}@c.us`;

            // Test the message sending
            const result = await this.sendMessageSafely(contactId, message);

            if (result) {
                console.log(`✅ Test message sent successfully to ${cleanedPhone}`);
                return { success: true, cleanedPhone, contactId };
            } else {
                console.log(`❌ Test message failed to ${cleanedPhone}`);
                return { success: false, cleanedPhone, contactId, error: 'Send failed' };
            }

        } catch (error) {
            console.error(`💥 Test send error:`, error.message);
            return { success: false, error: error.message };
        }
    }

    /**
     * Validate phone number format
     */
    validatePhoneNumber(phoneNumber) {
        const cleaned = this.businessDataManager.cleanPhoneNumber(phoneNumber);
        const isValid = cleaned && cleaned.length >= 11 && cleaned.startsWith('62');

        return {
            original: phoneNumber,
            cleaned: cleaned,
            isValid: isValid,
            contactId: isValid ? `${cleaned}@c.us` : null
        };
    }
}

module.exports = BlastManager;
