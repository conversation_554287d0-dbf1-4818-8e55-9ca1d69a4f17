/**
 * Zero Client Loss Follow-up System
 * Implements re-engagement sequences and multiple touchpoints
 */

class ZeroClientLoss {
    constructor() {
        // Follow-up sequence templates
        this.FOLLOW_UP_SEQUENCES = {
            // Day 1: Gentle check-in
            day_1: {
                messages: [
                    "Halo! Semoga harinya lancar 😊 Gimana, ada pertanyaan tentang yang kita bahas tadi?",
                    "Hi! Just checking - apakah ada yang perlu saya klarifikasi dari diskusi kita?",
                    "Halo! Mudah-mudahan tidak mengganggu ya. Ada yang mau ditanyakan lagi?"
                ],
                tone: 'gentle',
                urgency: 'low'
            },
            
            // Day 3: Value-add approach
            day_3: {
                messages: [
                    "Btw, saya baru ingat ada insight menarik yang cocok untuk bisnis Anda... 🤔",
                    "Oh ya, ada case study baru yang mirip banget sama situasi Anda. Mau saya share?",
                    "Saya dapat update tentang trend industri Anda yang mungkin menarik..."
                ],
                tone: 'helpful',
                urgency: 'medium'
            },
            
            // Day 7: Social proof and FOMO
            day_7: {
                messages: [
                    "Update: <PERSON><PERSON>n yang situasinya mirip Anda baru saja go-live dan hasilnya amazing! 🚀",
                    "FYI, kompetitor di industri Anda mulai banyak yang implementasi sistem serupa...",
                    "Just sharing: 3 klien baru minggu ini, semuanya dari referral klien yang puas 😊"
                ],
                tone: 'informative',
                urgency: 'medium'
            },
            
            // Day 14: Direct but caring approach
            day_14: {
                messages: [
                    "Halo! Saya masih mikirin challenge yang Anda ceritakan. Ada perkembangan?",
                    "Hi! Gimana kabar bisnisnya? Masalah yang kita diskusi masih jadi concern?",
                    "Checking in - apakah situasinya sudah berubah atau masih sama?"
                ],
                tone: 'caring',
                urgency: 'medium'
            },
            
            // Day 30: Fresh start approach
            day_30: {
                messages: [
                    "Halo! Sudah sebulan ya... Gimana perkembangan bisnisnya? 😊",
                    "Hi! Long time no chat. Ada update tentang challenge bisnis yang dulu kita bahas?",
                    "Halo! Semoga bisnis makin berkembang. Masih ingat diskusi kita dulu?"
                ],
                tone: 'friendly',
                urgency: 'low'
            }
        };

        // Re-engagement triggers based on last interaction
        this.RE_ENGAGEMENT_TRIGGERS = {
            showed_interest: [
                "Saya masih ingat antusiasme Anda tentang solusi ini. What happened?",
                "You seemed excited about the potential. Apa yang berubah?",
                "Dulu Anda bilang ini exactly what you need. Still relevant?"
            ],
            
            asked_questions: [
                "Masih ada pertanyaan yang belum terjawab dari diskusi kita?",
                "Apakah informasi yang saya berikan sudah cukup clear?",
                "Ada aspek lain yang perlu saya jelaskan lebih detail?"
            ],
            
            mentioned_budget: [
                "Tentang budget yang kita diskusi, ada update dari sisi finansial?",
                "Gimana dengan budget planning untuk project ini?",
                "Sudah ada clarity tentang investment untuk solusi ini?"
            ],
            
            mentioned_timeline: [
                "Bagaimana dengan timeline yang Anda mention dulu?",
                "Apakah timing-nya sudah lebih tepat sekarang?",
                "Update dong tentang schedule implementation yang direncanakan"
            ],
            
            expressed_concerns: [
                "Masih ada concern yang sama tentang implementasi?",
                "Apakah kekhawatiran yang dulu sudah teratasi?",
                "Gimana kalau kita address concern Anda satu per satu?"
            ]
        };

        // Open loop techniques to maintain curiosity
        this.OPEN_LOOPS = [
            "Ada strategi baru yang lagi trending di industri Anda...",
            "Klien kemarin cerita something interesting tentang ROI mereka...",
            "Saya dapat insight dari expert yang mungkin game-changing...",
            "Ada update teknologi yang bisa significantly impact bisnis Anda...",
            "Competitor analysis terbaru menunjukkan trend yang surprising..."
        ];

        // Multiple touchpoint strategies
        this.TOUCHPOINT_STRATEGIES = {
            educational: {
                approach: "Share valuable insights and industry knowledge",
                frequency: "Every 3-5 days",
                examples: [
                    "Sharing artikel tentang digital transformation",
                    "Industry report yang relevan",
                    "Case study dari bisnis serupa"
                ]
            },
            
            social_proof: {
                approach: "Share success stories and testimonials",
                frequency: "Weekly",
                examples: [
                    "Client success stories",
                    "Before/after transformations", 
                    "ROI achievements"
                ]
            },
            
            personal_touch: {
                approach: "Personal, caring check-ins",
                frequency: "Bi-weekly",
                examples: [
                    "Asking about business challenges",
                    "Sharing relevant opportunities",
                    "Offering help without selling"
                ]
            }
        };
    }

    /**
     * Generate follow-up message based on days since last contact
     */
    generateFollowUpMessage(daysSilent, lastInteractionType = 'general', emotionalState = 'neutral') {
        let sequence;
        
        // Determine which sequence to use
        if (daysSilent <= 1) {
            sequence = this.FOLLOW_UP_SEQUENCES.day_1;
        } else if (daysSilent <= 3) {
            sequence = this.FOLLOW_UP_SEQUENCES.day_3;
        } else if (daysSilent <= 7) {
            sequence = this.FOLLOW_UP_SEQUENCES.day_7;
        } else if (daysSilent <= 14) {
            sequence = this.FOLLOW_UP_SEQUENCES.day_14;
        } else {
            sequence = this.FOLLOW_UP_SEQUENCES.day_30;
        }

        // Get base message
        const messages = sequence.messages;
        let baseMessage = messages[Math.floor(Math.random() * messages.length)];

        // Add specific re-engagement trigger if available
        if (this.RE_ENGAGEMENT_TRIGGERS[lastInteractionType]) {
            const triggers = this.RE_ENGAGEMENT_TRIGGERS[lastInteractionType];
            const trigger = triggers[Math.floor(Math.random() * triggers.length)];
            baseMessage += `\n\n${trigger}`;
        }

        // Add open loop for curiosity
        if (daysSilent > 3) {
            const openLoop = this.OPEN_LOOPS[Math.floor(Math.random() * this.OPEN_LOOPS.length)];
            baseMessage += `\n\n${openLoop}`;
        }

        return {
            message: baseMessage,
            tone: sequence.tone,
            urgency: sequence.urgency,
            nextFollowUp: this.calculateNextFollowUp(daysSilent)
        };
    }

    /**
     * Calculate when next follow-up should happen
     */
    calculateNextFollowUp(currentDaysSilent) {
        if (currentDaysSilent < 1) return 1;
        if (currentDaysSilent < 3) return 3;
        if (currentDaysSilent < 7) return 7;
        if (currentDaysSilent < 14) return 14;
        return 30;
    }

    /**
     * Generate multiple touchpoint sequence
     */
    generateTouchpointSequence(prospectProfile) {
        const sequence = [];
        
        // Immediate follow-up (within 24 hours)
        sequence.push({
            day: 1,
            type: 'gentle_check_in',
            message: this.generateFollowUpMessage(1, prospectProfile.lastInteractionType).message,
            priority: 'high'
        });

        // Value-add touchpoint (day 3)
        sequence.push({
            day: 3,
            type: 'value_add',
            message: this.generateFollowUpMessage(3, prospectProfile.lastInteractionType).message,
            priority: 'high'
        });

        // Social proof touchpoint (day 7)
        sequence.push({
            day: 7,
            type: 'social_proof',
            message: this.generateFollowUpMessage(7, prospectProfile.lastInteractionType).message,
            priority: 'medium'
        });

        // Personal check-in (day 14)
        sequence.push({
            day: 14,
            type: 'personal_touch',
            message: this.generateFollowUpMessage(14, prospectProfile.lastInteractionType).message,
            priority: 'medium'
        });

        // Fresh start (day 30)
        sequence.push({
            day: 30,
            type: 'fresh_start',
            message: this.generateFollowUpMessage(30, prospectProfile.lastInteractionType).message,
            priority: 'low'
        });

        return sequence;
    }

    /**
     * Analyze conversation for re-engagement opportunities
     */
    analyzeReEngagementOpportunities(conversation) {
        const opportunities = {
            urgency: 'medium',
            bestApproach: 'value_add',
            specificTriggers: [],
            recommendedMessage: null,
            nextSteps: []
        };

        // Analyze last interaction type
        const lastMessages = conversation.messages.slice(-3);
        let lastInteractionType = 'general';

        lastMessages.forEach(msg => {
            if (msg.isFromUser) {
                const message = msg.message.toLowerCase();
                
                if (message.includes('interested') || message.includes('tertarik')) {
                    lastInteractionType = 'showed_interest';
                    opportunities.urgency = 'high';
                }
                if (message.includes('?')) {
                    lastInteractionType = 'asked_questions';
                }
                if (message.includes('budget') || message.includes('harga')) {
                    lastInteractionType = 'mentioned_budget';
                }
                if (message.includes('when') || message.includes('kapan')) {
                    lastInteractionType = 'mentioned_timeline';
                    opportunities.urgency = 'high';
                }
                if (message.includes('worried') || message.includes('khawatir')) {
                    lastInteractionType = 'expressed_concerns';
                }
            }
        });

        // Generate specific re-engagement message
        opportunities.recommendedMessage = this.generateFollowUpMessage(
            conversation.daysSinceLastMessage,
            lastInteractionType,
            conversation.lastEmotionalState
        );

        // Determine best approach
        if (conversation.conversionReadiness > 70) {
            opportunities.bestApproach = 'urgency_close';
        } else if (conversation.engagementLevel > 60) {
            opportunities.bestApproach = 'social_proof';
        } else if (conversation.objectionCount > 2) {
            opportunities.bestApproach = 'address_concerns';
        } else {
            opportunities.bestApproach = 'value_add';
        }

        return opportunities;
    }

    /**
     * Generate emergency re-engagement for high-value prospects
     */
    generateEmergencyReEngagement(prospectValue = 'high') {
        const emergencyMessages = {
            high: [
                "🚨 URGENT: Kompetitor Anda baru saja implementasi sistem serupa. Jangan sampai tertinggal!",
                "⚠️ BREAKING: Ada perubahan regulasi yang impact bisnis Anda. Need to discuss ASAP!",
                "🔥 LAST CHANCE: Rate khusus berakhir hari ini. Masih interested?",
                "💥 GAME CHANGER: Ada breakthrough teknologi yang perfect untuk bisnis Anda!"
            ],
            medium: [
                "⏰ Quick update: Ada opportunity yang time-sensitive untuk bisnis Anda",
                "🎯 FYI: Klien dengan profile serupa baru achieve ROI 400% dalam 3 bulan",
                "💡 Insight: Ada strategi baru yang bisa significantly boost revenue Anda",
                "🚀 Update: Implementation slot terbuka untuk bulan ini"
            ],
            low: [
                "😊 Hi! Gimana kabar bisnisnya? Masih ingat diskusi kita?",
                "👋 Long time no chat! Ada perkembangan di bisnis?",
                "🤔 Saya masih mikirin challenge yang Anda ceritakan dulu...",
                "💭 Curious - apakah situasinya sudah berubah?"
            ]
        };

        const messages = emergencyMessages[prospectValue] || emergencyMessages.medium;
        const selectedMessage = messages[Math.floor(Math.random() * messages.length)];

        return {
            message: selectedMessage,
            urgency: prospectValue === 'high' ? 'critical' : 'high',
            followUpIn: prospectValue === 'high' ? 4 : 24, // hours
            escalate: prospectValue === 'high'
        };
    }

    /**
     * Create conversation continuation strategies
     */
    createContinuationStrategy(conversation) {
        const strategy = {
            primaryApproach: null,
            backupApproaches: [],
            timeline: [],
            successMetrics: []
        };

        // Determine primary approach based on conversation analysis
        if (conversation.conversionReadiness > 80) {
            strategy.primaryApproach = 'assumptive_close';
            strategy.backupApproaches = ['urgency_close', 'alternative_close'];
        } else if (conversation.engagementLevel > 70) {
            strategy.primaryApproach = 'value_demonstration';
            strategy.backupApproaches = ['social_proof', 'case_study'];
        } else if (conversation.objectionCount > 0) {
            strategy.primaryApproach = 'objection_resolution';
            strategy.backupApproaches = ['risk_reversal', 'authority_proof'];
        } else {
            strategy.primaryApproach = 'relationship_building';
            strategy.backupApproaches = ['value_education', 'curiosity_creation'];
        }

        // Create timeline
        strategy.timeline = this.generateTouchpointSequence({
            lastInteractionType: this.determineLastInteractionType(conversation),
            conversionReadiness: conversation.conversionReadiness,
            engagementLevel: conversation.engagementLevel
        });

        return strategy;
    }

    /**
     * Determine last interaction type from conversation
     */
    determineLastInteractionType(conversation) {
        if (!conversation.messages || conversation.messages.length === 0) {
            return 'general';
        }

        const lastUserMessage = conversation.messages
            .filter(m => m.isFromUser)
            .slice(-1)[0];

        if (!lastUserMessage) return 'general';

        const message = lastUserMessage.message.toLowerCase();
        
        if (message.includes('interested') || message.includes('tertarik')) return 'showed_interest';
        if (message.includes('?')) return 'asked_questions';
        if (message.includes('budget') || message.includes('harga')) return 'mentioned_budget';
        if (message.includes('when') || message.includes('kapan')) return 'mentioned_timeline';
        if (message.includes('worried') || message.includes('concern')) return 'expressed_concerns';
        
        return 'general';
    }
}

module.exports = ZeroClientLoss;
