/**
 * Test script for the Renata handoff mechanism
 * Tests the functionality where users can type 'renata' to speak directly with Renata
 */

const { runAiAssistant } = require('./src/ai_assistant');

async function testRenataHandoff() {
    console.log('🧪 Testing Renata Handoff Mechanism...');
    console.log('=' .repeat(50));
    
    try {
        // Test 1: User types 'renata'
        console.log('\n📝 Test 1: User types "renata"');
        const response1 = await runAiAssistant('renata', 'test_user_1', 'Test User 1');
        console.log('✅ Response:', response1);
        
        // Test 2: User tries to continue conversation after handoff
        console.log('\n📝 Test 2: User tries to continue after handoff');
        const response2 = await runAiAssistant('Hello, are you still there?', 'test_user_1', 'Test User 1');
        console.log('✅ Response (should be null):', response2);
        
        // Test 3: User types 'speak with renata'
        console.log('\n📝 Test 3: User types "speak with renata"');
        const response3 = await runAiAssistant('speak with renata', 'test_user_2', 'Test User 2');
        console.log('✅ Response:', response3);
        
        // Test 4: User types 'talk to renata'
        console.log('\n📝 Test 4: User types "talk to renata"');
        const response4 = await runAiAssistant('talk to renata', 'test_user_3', 'Test User 3');
        console.log('✅ Response:', response4);
        
        // Test 5: Normal conversation (should work normally)
        console.log('\n📝 Test 5: Normal business inquiry');
        const response5 = await runAiAssistant('I need help with my business website', 'test_user_4', 'Test User 4');
        console.log('✅ Response (should be normal AI response):', response5 ? 'AI responded normally' : 'No response');
        
        console.log('\n🎉 All Renata handoff tests completed!');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

// Run the test
testRenataHandoff();