const axios = require('axios');
const dotenv = require('dotenv');

dotenv.config();

async function testGrokAPI() {
    console.log('🧪 Testing Grok API Connection...');
    console.log('API Key (first 10 chars):', process.env.AI_API_KEY?.substring(0, 10) + '...');
    
    try {
        const response = await axios.post('https://api.x.ai/v1/chat/completions', {
            model: 'grok-3-beta',
            messages: [
                {
                    role: "user",
                    content: "Hello, can you respond with just 'API working'?"
                }
            ],
            max_tokens: 10,
            temperature: 0.7
        }, {
            headers: {
                'Authorization': `Bearer ${process.env.AI_API_KEY}`,
                'Content-Type': 'application/json'
            },
            timeout: 30000
        });

        console.log('✅ API Response:', response.data);
        console.log('✅ Grok API is working correctly!');
        
    } catch (error) {
        console.error('❌ API Error:', error.response?.status, error.response?.statusText);
        console.error('❌ Error Details:', error.response?.data);
        console.error('❌ Full Error:', error.message);
        
        if (error.response?.status === 403) {
            console.log('\n🔍 Troubleshooting 403 Error:');
            console.log('1. Check if your API key is valid and active');
            console.log('2. Verify you have access to the xAI API beta');
            console.log('3. Ensure the API key format is correct (should start with "xai-")');
            console.log('4. Check if there are any billing or usage restrictions');
        }
    }
}

testGrokAPI();
