const BusinessDataManager = require('./src/business_data');
const LeadManager = require('./src/lead_manager');
const fs = require('fs');

async function setupChatbot() {
    console.log('🚀 Setting up WhatsApp Chatbot...\n');

    try {
        // Initialize business data manager
        console.log('📊 Initializing business data...');
        const businessDataManager = new BusinessDataManager();
        
        // Check if Excel file exists
        if (!fs.existsSync('filtered_business_data_no_chains_updated.xlsx')) {
            console.log('❌ Excel file not found: filtered_business_data_no_chains_updated.xlsx');
            console.log('   Please make sure the business data Excel file is in the root directory.');
            return;
        }

        // Convert Excel to JSON
        const businessData = await businessDataManager.convertExcelToJson();
        console.log(`✅ Converted ${businessData.length} business records to JSON`);

        // Initialize lead manager
        console.log('📝 Initializing lead management system...');
        const leadManager = new LeadManager();
        console.log('✅ Lead management system ready');

        // Display statistics
        const stats = businessDataManager.getStatistics();
        console.log('\n📈 Business Data Statistics:');
        console.log(`   Total businesses: ${stats.total}`);
        console.log(`   Ready for blast: ${stats.uncontacted}`);
        console.log(`   Already contacted: ${stats.contacted}`);

        console.log('\n🎉 Setup completed successfully!');
        console.log('\n📋 Available Commands:');
        console.log('   • Send "blast-bisnis" to start WhatsApp blast');
        console.log('   • Send "stats-lead" to view statistics');
        console.log('   • Regular chat will use Gemini AI');

        console.log('\n⚠️  Important Notes:');
        console.log('   • Make sure your WhatsApp is connected');
        console.log('   • Only authorized numbers can send blasts');
        console.log('   • Business responses will be automatically processed');
        console.log('   • Interested leads will be saved for follow-up');

    } catch (error) {
        console.error('❌ Setup failed:', error.message);
        console.log('\nPlease check:');
        console.log('1. Excel file exists and is readable');
        console.log('2. All dependencies are installed (npm install)');
        console.log('3. Environment variables are set correctly');
    }
}

// Run setup if called directly
if (require.main === module) {
    setupChatbot();
}

module.exports = { setupChatbot };
