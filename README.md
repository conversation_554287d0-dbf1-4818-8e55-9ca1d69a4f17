# WhatsApp Business Chatbot with AI Assistant

A powerful WhatsApp chatbot for business consultation that uses AI for conversational responses and includes automated business outreach capabilities.

## 🚀 Features

### Core Chatbot Features
- **AI Assistant Integration**: Advanced conversational AI with content filtering
- **WhatsApp Web.js**: Reliable WhatsApp integration
- **Admin Mode**: Direct communication with human administrators
- **Working Hours Detection**: Automatic responses based on business hours
- **Typing Indicators**: Realistic typing simulation
- **Content Filtering**: Automatic detection and redirection of inappropriate topics
- **Business Focus**: All conversations directed toward business consultation services

### Business Outreach System
- **WhatsApp Blast**: Automated messaging to business contacts
- **Lead Management**: Automatic lead capture and qualification
- **Business Data Management**: Excel to JSON conversion and management
- **Response Processing**: Intelligent handling of business responses
- **Statistics Tracking**: Comprehensive analytics and reporting

## 📋 Prerequisites

- Node.js (v14 or higher)
- WhatsApp account for bot
- AI API key (currently supports Google Gemini)
- Business data in Excel format (`filtered_business_data_no_chains_updated.xlsx`)

## 🛠️ Installation & Setup

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Configure environment variables**
   Update the `.env` file with:
   - `AI_API_KEY`: Your AI provider API key
   - `WHATSAPP_BOT_NUMBER`: Your bot's WhatsApp number for client communication
   - `ADMIN_CONTACT`: Admin contact number for bot management
   - `LEADS_FILE`: Lead storage file path

3. **Run setup to process business data**
   ```bash
   npm run setup
   ```

4. **Start the bot**
   ```bash
   npm start
   ```

## 📱 Usage Commands

### For Authorized Admin (configured in ADMIN_CONTACT)
- **`test-blast`** - Send test message for testing blast functionality
- **`blast-bisnis`** - Start WhatsApp blast to all businesses
- **`analytics`** or **`stats-analytics`** - View comprehensive analytics report
- **`export-analytics`** - Export analytics data to CSV files
- **`stats-lead`** - View lead statistics

### For All Users
- **`admin`** - Enter admin mode for human assistance
- Any other message - Processed by AI Assistant with business focus and content filtering

> 📖 **For detailed testing procedures and configuration guide, see [TESTING_GUIDE.md](TESTING_GUIDE.md)**

## 🏢 Business Outreach Features

### WhatsApp Blast System
- Sends professional sales messages to businesses
- Offers web development, system development, data analysis services
- Automatic lead qualification from responses
- Batch processing with delays to avoid blocking

### Lead Management
- Captures interested businesses automatically
- Tracks conversation history
- Manages follow-up requirements
- Exports lead data for CRM

## 📊 What Changed in This Refactor

### ❌ Removed (Old System)
- Google Cloud Dialogflow integration
- Google Sheets message storage
- Google Drive dependencies
- Complex API dependencies

### ✅ Added (New System)
- Pure Google Gemini AI integration
- Local JSON data storage
- Business data processing from Excel
- WhatsApp blast functionality
- Comprehensive lead management
- Advanced analytics and reporting

### 🎯 Business Benefits
- **Direct Sales Outreach**: Automated messaging to potential clients
- **Lead Qualification**: Smart detection of interested businesses
- **Professional Templates**: Multiple sales message options
- **Analytics**: Track success rates and conversions
- **Local Control**: No external API dependencies for data storage

## 📞 Contact & Support

**Admin Contact**: +62 822-1049-3145

**Services Offered**:
- Web Development
- System Development
- Data Analysis
- Dashboard Development

---
*Refactored from Dialogflow-based system to pure Gemini AI with business outreach capabilities*