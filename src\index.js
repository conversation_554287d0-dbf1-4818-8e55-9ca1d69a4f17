//inisiasi watsapp web js
const { Client, LocalAuth } = require("whatsapp-web.js");

const qrcode = require("qrcode-terminal");

// Handle uncaught exceptions and unhandled rejections
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  if (error.message.includes('Execution context was destroyed')) {
    console.log('Detected execution context error - attempting graceful restart...');
    setTimeout(() => {
      process.exit(1);
    }, 2000);
  }
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  if (reason && reason.message && reason.message.includes('Execution context was destroyed')) {
    console.log('Detected execution context error in promise - attempting graceful restart...');
    setTimeout(() => {
      process.exit(1);
    }, 2000);
  }
});

const axios = require("axios");
const { v4: uuidv4 } = require("uuid");
const { runAiAssistant } = require("./ai_assistant");
const { cekLocalMessage } = require("./message_local");
const { replaceMultipleStringsAll } = require("./replace-string.js");
const BusinessDataManager = require("./business_data");
const LeadManager = require("./lead_manager");
const BlastManager = require("./blast_manager");
const AnalyticsManager = require("./analytics_manager");
const { runTestBlast } = require("../test_blast");
const delay = (time) => new Promise((resolve) => setTimeout(resolve, time));

// Helper function to check if client is ready
function isClientReady() {
  try {
    return client && client.info && client.info.wid && client.readyTimestamp;
  } catch (error) {
    return false;
  }
}

// Helper function for robust message sending
async function sendMessageSafely(contactId, messageText, chat = null) {
  // Check if client is ready
  if (!isClientReady()) {
    console.error("Client is not ready for sending messages");
    return false;
  }

  // Ensure messageText is a string and properly formatted
  const cleanMessage = String(messageText).trim();

  if (!cleanMessage) {
    console.error("Cannot send empty message");
    return false;
  }

  // Validate contactId format
  if (!contactId || typeof contactId !== 'string') {
    console.error("Invalid contactId provided");
    return false;
  }

  // Method 1: Try using client.sendMessage with proper error handling
  try {
    console.log(`Attempting to send message to ${contactId}: ${cleanMessage.substring(0, 50)}...`);
    const result = await client.sendMessage(contactId, cleanMessage);
    console.log("Message sent successfully via client.sendMessage");
    return true;
  } catch (sendError) {
    console.error("Failed to send message via client.sendMessage:", sendError.message);

    // Method 2: Try alternative method if chat object is available
    if (chat) {
      try {
        console.log("Trying alternative method via chat.sendMessage...");
        const result = await chat.sendMessage(cleanMessage);
        console.log("Message sent successfully via chat.sendMessage");
        return true;
      } catch (altSendError) {
        console.error("Failed to send message via chat.sendMessage:", altSendError.message);
      }
    }

    // Method 3: Try getting fresh chat object and sending
    try {
      console.log("Trying to get fresh chat object...");
      const freshChat = await client.getChatById(contactId);
      if (freshChat) {
        const result = await freshChat.sendMessage(cleanMessage);
        console.log("Message sent successfully via fresh chat object");
        return true;
      }
    } catch (freshChatError) {
      console.error("Failed to send message via fresh chat object:", freshChatError.message);
    }

    // Method 4: Try with a delay and retry
    try {
      console.log("Waiting 2 seconds before retry...");
      await new Promise(resolve => setTimeout(resolve, 2000));
      const result = await client.sendMessage(contactId, cleanMessage);
      console.log("Message sent successfully after retry");
      return true;
    } catch (retryError) {
      console.error("Failed to send message after retry:", retryError.message);
    }

    console.error("All message sending methods failed");
    return false;
  }
}

// Function to check if current time is during work hours (Monday-Friday, 8:00-15:30)
function isWorkingHours() {
  const now = new Date();
  const day = now.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
  const hour = now.getHours();
  const minute = now.getMinutes();

  // Check if it's Monday to Friday (1-5)
  const isWeekday = day >= 1 && day <= 5;

  // Check if it's between 8:00 and 15:30
  const isWorkTime = (hour >= 8 && hour < 15) || (hour === 15 && minute <= 30);

  return isWeekday && isWorkTime;
}

// Dictionary to store user states
const userState = {};

// Function to create client with proper error handling
function createWhatsAppClient() {
  return new Client({
    authStrategy: new LocalAuth(),
    puppeteer: {
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
      ],
      timeout: 0, // Remove timeout to prevent premature termination
      defaultViewport: null,
      ignoreDefaultArgs: ['--disable-extensions'],
      handleSIGINT: false,
      handleSIGTERM: false,
      handleSIGHUP: false
    },
    // Remove webVersionCache to let it use default
    takeoverOnConflict: true,
    takeoverTimeoutMs: 60000,
  });
}

const client = createWhatsAppClient();

// Initialize environment variables
const dotenv = require("dotenv");
dotenv.config();

// Helper function to normalize phone numbers for admin comparison
function normalizePhoneNumber(phone) {
  if (!phone) return '';

  // Remove all non-numeric characters
  let cleaned = phone.toString().replace(/\D/g, '');

  // Handle Indonesian phone numbers - normalize to international format
  if (cleaned.startsWith('0')) {
    cleaned = '62' + cleaned.substring(1);
  } else if (cleaned.startsWith('8')) {
    cleaned = '62' + cleaned;
  } else if (!cleaned.startsWith('62')) {
    cleaned = '62' + cleaned;
  }

  return cleaned;
}

// Helper function to check if a contact is admin
function isAdminContact(contactId) {
  const nomorPengguna = contactId.replace("@c.us", "");
  const normalizedUser = normalizePhoneNumber(nomorPengguna);
  const normalizedAdmin = normalizePhoneNumber(process.env.ADMIN_CONTACT);

  console.log(`🔍 Admin check: User "${nomorPengguna}" → "${normalizedUser}" vs Admin "${process.env.ADMIN_CONTACT}" → "${normalizedAdmin}"`);

  return normalizedUser === normalizedAdmin;
}

// Initialize business data and lead// Initialize managers
const businessDataManager = new BusinessDataManager();
const leadManager = new LeadManager();
const analyticsManager = new AnalyticsManager();

// Message batching system
const messageBatches = new Map();
const BATCH_TIMEOUT = 8000; // 8 seconds to wait for additional messages
const MAX_BATCH_SIZE = 5; // Maximum messages to batch together
let blastManager = null; // Will be initialized after client is ready

console.log("🚀 Starting WhatsApp Chatbot...");
console.log("📦 WhatsApp Web.js version: 1.31.0");
console.log("🔧 Initializing client...");

client.on("qr", (qr) => {
  console.log("📱 QR Code generated! Please scan with your WhatsApp mobile app:");
  qrcode.generate(qr, {
    small: true,
  });
  console.log("⏳ Waiting for QR code scan...");
});

client.on("ready", async () => {
  console.log("✅ Client is ready!");
  console.log("🤖 WhatsApp Chatbot is now online and ready to receive messages!");

  // Initialize blast manager with the ready client
  blastManager = new BlastManager(client);
  console.log("💼 Blast Manager initialized");

  // Initialize business data
  try {
    await businessDataManager.loadBusinessData();
    const stats = businessDataManager.getStatistics();
    console.log(`📊 Business data loaded: ${stats.total} businesses (${stats.uncontacted} uncontacted)`);
  } catch (error) {
    console.log("⚠️  Business data not loaded. Run 'npm run setup' first.");
  }

  // Log current working hours status
  const now = new Date();
  console.log(`⏰ Current time: ${now.toLocaleString('id-ID', { timeZone: 'Asia/Jakarta' })}`);
  console.log(`🕐 Is working hours: ${isWorkingHours()}`);

  // Set the exact timestamp when bot becomes ready (not rounded to hour)
  // This ensures only messages received AFTER this moment are processed
  client.readyTimestamp = new Date();

  console.log("📱 Bot ready timestamp set:", client.readyTimestamp.toISOString());
  console.log("\n🎯 Ready for business! Available commands:");
  console.log("   • blast-bisnis - Start WhatsApp blast");
  console.log("   • stats-lead - View statistics");
  console.log("   • analytics - View comprehensive analytics");
  console.log("   • export-analytics - Export analytics to CSV");
  console.log("   • admin - Admin mode");
});

client.on("authenticated", () => {
  console.log("AUTHENTICATED");
});

client.on("auth_failure", (msg) => {
  console.error("AUTHENTICATION FAILURE", msg);
});

client.on("disconnected", (reason) => {
  console.log("Client was logged out", reason);
});

// Add error handling for initialization failures
client.on("error", (error) => {
  console.error("Client error:", error);
  if (error.message.includes("Execution context was destroyed")) {
    console.log("Execution context destroyed - attempting to reinitialize...");
    setTimeout(() => {
      initializeClientWithRetry();
    }, 5000);
  }
});

client.on("message", async (message) => {
  // Memeriksa apakah pesan diterima setelah bot siap
  const messageTime = new Date(message.timestamp * 1000);
  const readyTime = client.readyTimestamp;

  if (messageTime.getTime() > readyTime.getTime()) {
    // Proses pesan di sini
    // update status bot menjadi aktif
    client.sendPresenceAvailable();

    // Retrieve the contact information
    const contact = await message.getContact();

    // Process the message
    await saveMessage(message);

    // update status bot menjadi tidak aktif
    client.sendPresenceUnavailable();
  } else {
    console.log(`📅 Old message ignored: Message time ${messageTime.toISOString()} vs Ready time ${readyTime.toISOString()}`);
  }
});

// Business response keywords for lead qualification
const POSITIVE_KEYWORDS = ['tertarik', 'interested', 'ya', 'yes', 'iya', 'mau', 'butuh', 'perlu', 'info', 'informasi', 'konsultasi', 'gratis'];
const NEGATIVE_KEYWORDS = ['tidak', 'no', 'nggak', 'gak', 'tolak', 'refuse', 'maaf', 'sorry'];

// async function saveMessage(message) {
//   try {
//     // ambil data contact
//     const contact = await message.getContact();
//     const contactId = contact.id._serialized; // Unique ID for the contact

//     // If user is in "admin mode" or paused mode
//     if (userState[contactId] === "admin" || userState[contactId] === "paused") {

//       // If user types 'selesai', return to chatbot mode
//       if (message.body.toLowerCase() === "selesai") {
//         userState[contactId] = "bot"; // Switch back to bot mode
//         await client.sendMessage(contactId, "Anda sekarang kembali berinteraksi dengan chatbot. Ada yang bisa saya bantu?");
//         return;
//       }

//       // If user is in paused mode, stop chatbot responses until they type 'selesai'
//       if (userState[contactId] === "paused") {
//         return; // Stop further processing by the bot when in paused mode
//       }

//       // If user types 'mitra', send mitra information and pause the bot
//       if (message.body.toLowerCase() === "mitra") {
//         await client.sendMessage(contactId, "Silakan kunjungi link berikut untuk informasi perekrutan: http://s.bps.go.id/rekrutmen2171. Perlu diperhatikan bahwa ini **bukan** perekrutan PPPK, PNS, atau Honorer.\n\nAdmin akan segera menjawab pertanyaan Anda pada jam kerja (Senin-Jumat, pukul 08.00 - 16.00). Mohon ditunggu.\n\nUntuk mengakhiri sesi ini, silakan ketik 'selesai'."
//         );
//         userState[contactId] = "paused"; // Enter paused mode
//         return; // Stop further processing by the bot
//       }

//       // If user types 'pelayanan', send pelayanan information and pause the bot
//       if (message.body.toLowerCase() === "pelayanan") {
//         await client.sendMessage(contactId, "Admin akan segera menjawab pertanyaan Anda terkait Pelayanan Statistik pada jam kerja (Senin-Jumat, pukul 08.00 - 16.00). Mohon ditunggu.\n\nUntuk mengakhiri sesi ini, silakan ketik 'selesai'."
//         );
//         userState[contactId] = "paused"; // Enter paused mode
//         return; // Stop further processing by the bot
//       }

//       // Default response for when in admin mode
//       await client.sendMessage(contactId, "Anda sedang dalam mode admin. Ketik 'mitra' untuk informasi Mitra atau 'pelayanan' untuk Pelayanan Statistik. Ketik 'selesai' untuk kembali ke chatbot.");
//       return;
//     }

//     // If user sends 'admin', switch to admin chat mode
//     if (message.body.toLowerCase() === "admin") {
//       userState[contactId] = "admin"; // Set state to admin mode
//       await client.sendMessage(contactId, "Anda akan dihubungkan dengan admin, mohon pilih opsi berikut:\n1. Ketik 'mitra' untuk informasi Mitra.\n2. Ketik 'pelayanan' untuk Pelayanan Statistik.\n\nKetik 'selesai' untuk melanjutkan interaksi dengan chatbot.");
//       return; // Stop further processing by the bot
//     }

//     // Process message with bot if user is in "bot mode"
//     if (message.id.remote.includes("@c.us") && message.type === "chat") {
//       await useTemplateMessageKawan(message, contact);
//     }
//   } catch (error) {
//     console.log(error);
//   }
// }

async function modeTyping(message, replyMessage, contactId) {
  try {
    // Log the process
    console.log("masuk fungsi modeTyping");

    // Validate inputs
    if (!replyMessage || typeof replyMessage !== 'string') {
      console.error("Invalid reply message provided to modeTyping");
      return false;
    }

    // Retrieve the chat for sending seen and typing state
    const chat = await message.getChat();
    if (!chat) {
      console.error("Could not retrieve chat object");
      return false;
    }

    // Mark the message as seen (with error handling)
    try {
      await chat.sendSeen();
    } catch (seenError) {
      console.warn("Failed to mark message as seen:", seenError.message);
    }

    // Set the bot as typing (with error handling)
    try {
      await chat.sendStateTyping();
    } catch (typingError) {
      console.warn("Failed to set typing state:", typingError.message);
    }

    // Calculate typing time based on the length of the message (more realistic)
    const typingTime = Math.min(Math.max((replyMessage.length / 100) * 1000, 1000), 3000);

    // Wait for the typing time
    await new Promise((resolve) => setTimeout(resolve, typingTime));

    // Send the message to the user with error handling
    const messageSent = await sendMessageSafely(contactId, replyMessage, chat);

    // Stop typing state after sending the message (with error handling)
    try {
      await chat.clearState();
    } catch (clearError) {
      console.warn("Failed to clear typing state:", clearError.message);
    }

    return messageSent;
  } catch (error) {
    console.error(`Error in modeTyping: ${error.message}`);
    return false;
  }
}

async function saveMessage(message) {
  try {
    // Retrieve the contact information
    const contact = await message.getContact();
    const contactId = contact.id._serialized; // Unique ID for the contact

    // Handle admin and paused modes
    if (userState[contactId] === "admin" || userState[contactId] === "paused") {
      // If the user types 'selesai', return to bot mode
      if (message.body.toLowerCase() === "selesai") {
        userState[contactId] = "bot"; // Switch back to bot mode
        const replyMessage =
          "Anda sekarang kembali berinteraksi dengan chatbot. Ada yang bisa saya bantu?";
        await modeTyping(message, replyMessage, contactId);
        return;
      }

      // If the user is in paused mode, do not process messages
      if (userState[contactId] === "paused") {
        return; // Stop processing
      }

      // Handle 'layanan' response - Digital services information
      if (message.body.toLowerCase() === "layanan" || message.body.toLowerCase() === "services") {
        let replyMessage;

        if (isWorkingHours()) {
          // During work hours: Add proactive message and remove waiting message
          replyMessage = "🌟 *LAYANAN DIGITAL KAMI*\n\n" +
            "💻 *Website Development*\n" +
            "📱 *Mobile App Development*\n" +
            "🎯 *Digital Marketing*\n" +
            "📊 *Data Analytics*\n" +
            "🤖 *AI Solutions*\n\n" +
            "Admin akan segera menghubungi Anda untuk konsultasi GRATIS. Untuk mengakhiri sesi ini, silakan ketik 'selesai'.";
        } else {
          // Outside work hours: Keep the waiting message
          replyMessage = "🌟 *LAYANAN DIGITAL KAMI*\n\n" +
            "💻 *Website Development*\n" +
            "📱 *Mobile App Development*\n" +
            "🎯 *Digital Marketing*\n" +
            "📊 *Data Analytics*\n" +
            "🤖 *AI Solutions*\n\n" +
            "Admin akan segera menjawab pertanyaan Anda pada jam kerja (Senin-Jumat, pukul 08.00 - 17.00). Mohon ditunggu.\n\nUntuk mengakhiri sesi ini, silakan ketik 'selesai'.";
        }

        await modeTyping(message, replyMessage, contactId);
        userState[contactId] = "paused"; // Enter paused mode
        return; // Stop further processing
      }

      // Handle 'konsultasi' response - Business consultation
      if (message.body.toLowerCase() === "konsultasi" || message.body.toLowerCase() === "consultation") {
        let replyMessage;

        if (isWorkingHours()) {
          // During work hours: Add proactive message and remove waiting message
          replyMessage = "💼 *KONSULTASI BISNIS DIGITAL*\n\n" +
            "🎯 *Strategi Digital Marketing*\n" +
            "📈 *Analisis Performa Bisnis*\n" +
            "🔧 *Optimasi Sistem & Proses*\n" +
            "💡 *Inovasi Teknologi Bisnis*\n" +
            "📊 *Data-Driven Decision Making*\n\n" +
            "💰 *Konsultasi GRATIS untuk klien baru!*\n\n" +
            "Admin akan segera menghubungi Anda. Untuk mengakhiri sesi ini, silakan ketik 'selesai'.";
        } else {
          // Outside work hours: Keep the admin availability message
          replyMessage = "💼 *KONSULTASI BISNIS DIGITAL*\n\n" +
            "🎯 *Strategi Digital Marketing*\n" +
            "📈 *Analisis Performa Bisnis*\n" +
            "🔧 *Optimasi Sistem & Proses*\n" +
            "💡 *Inovasi Teknologi Bisnis*\n" +
            "📊 *Data-Driven Decision Making*\n\n" +
            "💰 *Konsultasi GRATIS untuk klien baru!*\n\n" +
            "Admin akan segera menjawab pertanyaan Anda pada jam kerja (Senin-Jumat, pukul 08.00 - 17.00). Mohon ditunggu.\n\nUntuk mengakhiri sesi ini, silakan ketik 'selesai'.";
        }

        await modeTyping(message, replyMessage, contactId);
        userState[contactId] = "paused"; // Enter paused mode
        return; // Stop further processing
      }

      // Handle 'support' response - Customer support
      if (message.body.toLowerCase() === "support" || message.body.toLowerCase() === "bantuan") {
        let replyMessage;

        if (isWorkingHours()) {
          // During work hours: Add proactive message and remove waiting message
          replyMessage = "🛠️ *CUSTOMER SUPPORT*\n\n" +
            "📞 *Technical Support*\n" +
            "🔧 *Maintenance & Updates*\n" +
            "📚 *Training & Documentation*\n" +
            "💬 *Live Chat Support*\n" +
            "🎯 *Project Management*\n\n" +
            "⚡ *Response time: < 2 jam*\n\n" +
            "Admin akan segera menghubungi Anda. Untuk mengakhiri sesi ini, silakan ketik 'selesai'.";
        } else {
          // Outside work hours: Keep the waiting message
          replyMessage = "🛠️ *CUSTOMER SUPPORT*\n\n" +
            "📞 *Technical Support*\n" +
            "🔧 *Maintenance & Updates*\n" +
            "📚 *Training & Documentation*\n" +
            "💬 *Live Chat Support*\n" +
            "🎯 *Project Management*\n\n" +
            "⚡ *Response time: < 2 jam*\n\n" +
            "Admin akan segera menanggapi pertanyaan Anda pada jam kerja (Senin-Jumat, pukul 08.00 - 17.00). Mohon menunggu.\n\nUntuk mengakhiri sesi ini, silakan ketik 'selesai'.";
        }

        await modeTyping(message, replyMessage, contactId);
        userState[contactId] = "paused"; // Enter paused mode
        return; // Stop further processing
      }

      // Handle 'portfolio' response - Show portfolio and case studies
      if (message.body.toLowerCase() === "portfolio" || message.body.toLowerCase() === "portofolio") {
        const replyMessage =
          "🎨 *PORTFOLIO RENATA HENESSA*\n\n" +
          "🏢 *Business Systems (10+ Projects)*\n" +
          "• Sistem manajemen gudang terintegrasi supplier-warehouse-store\n" +
          "• Custom ERP untuk manufaktur dengan ROI 300%\n" +
          "• Sistem inventory real-time untuk retail chain\n\n" +
          "🤖 *WhatsApp Chatbot Solutions*\n" +
          "• Chatbot seperti yang sedang Anda gunakan ini\n" +
          "• Sales automation & lead qualification system\n" +
          "• Customer service 24/7 dengan AI integration\n\n" +
          "📊 *Business Analytics & Data*\n" +
          "• Dashboard business intelligence untuk decision making\n" +
          "• Analisis data yang meningkatkan profit 250%\n" +
          "• Reporting otomatis untuk management\n\n" +
          "⚡ *Business Automation*\n" +
          "• Otomasi proses operasional (efisiensi +180%)\n" +
          "• Workflow management system\n" +
          "• Integration antar sistem existing\n\n" +
          "🌐 *Website Development*\n" +
          "• Website bisnis yang fokus konversi (+300% leads)\n" +
          "• E-commerce platform dengan payment gateway\n" +
          "• Landing page high-converting\n\n" +
          "🔗 *Portfolio lengkap: renatahenessa.com*\n" +
          `📞 Konsultasi: ${process.env.WHATSAPP_BOT_NUMBER}\n\n` +
          "Admin akan menjawab pertanyaan lanjutan pada jam kerja. Ketik 'selesai' untuk kembali ke chatbot.";
        await modeTyping(message, replyMessage, contactId);
        userState[contactId] = "paused"; // Enter paused mode
        return; // Stop further processing
      }

      // Default response in admin mode - restored to basic functionality
      const replyMessage =
        "Anda sedang dalam mode admin. Silakan pilih opsi berikut:\n1. Ketik 'mitra' untuk informasi Mitra.\n2. Ketik 'pelayanan' untuk Pelayanan Statistik.\n3. Ketik 'pengaduan' untuk Pelayanan Pengaduan.\n\nKetik 'selesai' untuk kembali berinteraksi dengan chatbot.";
      await modeTyping(message, replyMessage, contactId);
      return;
    }

    // Handle when the user types 'admin'
    if (message.body.toLowerCase() === "admin") {
      userState[contactId] = "admin"; // Set state to admin mode
      const replyMessage =
        "🤖 *ADMIN MODE ACTIVATED*\n\n" +
        "Anda akan dihubungkan dengan admin. Silakan pilih opsi berikut:\n\n" +
        "1️⃣ Ketik 'layanan' untuk informasi Layanan Digital\n" +
        "2️⃣ Ketik 'konsultasi' untuk Konsultasi Bisnis\n" +
        "3️⃣ Ketik 'support' untuk Customer Support\n" +
        "4️⃣ Ketik 'portfolio' untuk melihat Portfolio\n\n" +
        "💬 Atau langsung tulis pertanyaan Anda\n\n" +
        "Ketik 'selesai' untuk kembali ke chatbot AI.";
      await modeTyping(message, replyMessage, contactId);
      return; // Stop further processing
    }

    // Business keyword detection for lead qualification
    // Check if message contains business-related keywords for automatic lead processing

    // Process the message if the user is in bot mode
    if (message.id.remote.includes("@c.us") && message.type === "chat") {
      // Check for analytics command
      if (message.body.toLowerCase().startsWith("analytics") || message.body.toLowerCase().startsWith("stats-analytics")) {
        if (isAdminContact(contactId)) {
          try {
            const report = analyticsManager.generateReport();
            const analyticsMessage = `📊 *ANALYTICS REPORT*\n\n` +
              `📈 *Overview:*\n` +
              `• Total Conversations: ${report.overview.totalConversations}\n` +
              `• Converted: ${report.overview.convertedConversations}\n` +
              `• Conversion Rate: ${report.overview.conversionRate}%\n` +
              `• Active Conversations: ${report.overview.activeConversations}\n` +
              `• Avg Messages/Conversation: ${report.overview.avgMessagesPerConversation}\n\n` +
              `🎯 *Conversion Funnel:*\n` +
              Object.entries(report.conversionFunnel.funnel).map(([stage, count]) =>
                `• ${stage}: ${count}`).join('\n') + '\n\n' +
              `⏱️ *Time to Conversion:*\n` +
              `• Average: ${report.timeToConversion.avgTimeToConversion} minutes\n\n` +
              `🔍 *Top Pain Points:*\n` +
              report.painPointAnalysis.topPainPoints.slice(0, 3).map(([point, freq]) =>
                `• ${point.substring(0, 30)}... (${freq}x)`).join('\n') + '\n\n' +
              `💡 *Recommendations:*\n` +
              report.recommendations.slice(0, 2).map(rec => `• ${rec.message}`).join('\n');

            await modeTyping(message, analyticsMessage, contactId);
          } catch (error) {
            await modeTyping(message, "Error generating analytics report: " + error.message, contactId);
          }
          return;
        } else {
          await modeTyping(message, "Unauthorized. Only admin can access analytics.", contactId);
          return;
        }
      }

      // Check for export analytics command
      if (message.body.toLowerCase().startsWith("export-analytics")) {
        if (isAdminContact(contactId)) {
          try {
            const csvData = analyticsManager.exportAnalyticsToCSV();
            const conversationCsvData = analyticsManager.conversationManager.exportToCSV();

            // Save to files
            const timestamp = new Date().toISOString().split('T')[0];
            const analyticsFileName = `analytics_report_${timestamp}.csv`;
            const conversationsFileName = `conversations_export_${timestamp}.csv`;

            require('fs').writeFileSync(analyticsFileName, csvData);
            require('fs').writeFileSync(conversationsFileName, conversationCsvData);

            const exportMessage = `📊 *ANALYTICS EXPORTED*\n\n` +
              `✅ Files created:\n` +
              `• ${analyticsFileName}\n` +
              `• ${conversationsFileName}\n\n` +
              `📁 Check your project directory for the CSV files.`;

            await modeTyping(message, exportMessage, contactId);
          } catch (error) {
            await modeTyping(message, "Error exporting analytics: " + error.message, contactId);
          }
          return;
        } else {
          await modeTyping(message, "Unauthorized. Only admin can export analytics.", contactId);
          return;
        }
      }

      // Check for test blast command
      if (message.body.toLowerCase().startsWith("test-blast")) {
        // Check if sender is authorized
        if (isAdminContact(contactId)) {
          if (!blastManager) {
            await modeTyping(message, "Blast manager belum siap. Silakan tunggu sebentar.", contactId);
            return;
          }

          const replyMessage = `🧪 Memulai TEST BLAST ke nomor test...\n\nIni adalah test untuk melihat bagaimana sistem menangani respon bisnis dan kualifikasi lead.\n\nSilakan jawab pesan yang akan Anda terima untuk menguji sistem AI.`;
          await modeTyping(message, replyMessage, contactId);

          try {
            const result = await runTestBlast(client);

            const summaryMessage = `🎉 Test blast selesai!\n\n` +
              `✅ Berhasil dikirim: ${result.sent}\n` +
              `❌ Gagal: ${result.failed}\n\n` +
              `Sekarang coba balas pesan yang diterima di ${process.env.TESTING_PHONE_NUMBER} dengan:\n` +
              `• "tertarik" - untuk test respon positif\n` +
              `• "tidak" - untuk test respon negatif\n` +
              `• pesan lain - untuk test respon netral\n\n` +
              `Sistem akan otomatis memproses respon dan membuat lead jika tertarik.`;

            await sendMessageSafely(contactId, summaryMessage);
          } catch (error) {
            console.error('Test blast error:', error);
            await sendMessageSafely(contactId, "❌ Terjadi kesalahan saat mengirim test blast. Silakan coba lagi.");
          }
          return;
        } else {
          await modeTyping(message, "🚫 Maaf, Anda tidak memiliki izin untuk mengirim test blast.", contactId);
          return;
        }
      }

      // Check for business blast command
      if (message.body.toLowerCase().startsWith("blast-bisnis")) {
        // Check if sender is authorized
        if (isAdminContact(contactId)) {
          if (!blastManager) {
            await modeTyping(message, "Blast manager belum siap. Silakan tunggu sebentar.", contactId);
            return;
          }

          const replyMessage = "🚀 Memulai WhatsApp blast ke bisnis-bisnis...\n\nProses ini akan mengirim pesan penawaran layanan ke semua bisnis yang belum dihubungi. Estimasi waktu: beberapa menit.\n\nAnda akan mendapat notifikasi ketika selesai.";
          await modeTyping(message, replyMessage, contactId);

          try {
            // Start the blast with default template
            const result = await blastManager.sendBlast('complete_package', {
              batchSize: 5,
              delayBetweenMessages: 3000,
              delayBetweenBatches: 30000
            });

            const duration = Math.round((result.endTime - result.startTime) / 1000);
            const summaryMessage = `🎉 Blast selesai dalam ${duration} detik!\n\n` +
              `✅ Berhasil dikirim: ${result.sent}\n` +
              `❌ Gagal: ${result.failed}\n` +
              `📊 Success rate: ${((result.sent / result.total) * 100).toFixed(1)}%\n\n` +
              `Sekarang tunggu respon dari bisnis-bisnis tersebut. Lead yang tertarik akan otomatis tersimpan.`;

            await sendMessageSafely(contactId, summaryMessage);
          } catch (error) {
            console.error('Blast error:', error);
            await sendMessageSafely(contactId, "❌ Terjadi kesalahan saat mengirim blast. Silakan coba lagi atau periksa log untuk detail.");
          }
          return;
        } else {
          await modeTyping(message, "🚫 Maaf, Anda tidak memiliki izin untuk mengirim blast bisnis.", contactId);
          return;
        }
      }

      // Check for test phone number command
      if (message.body.toLowerCase().startsWith("test-phone ")) {
        if (isAdminContact(contactId)) {
          const phoneNumber = message.body.substring(11).trim();

          if (!phoneNumber) {
            await modeTyping(message, "❌ Format: test-phone [nomor]\nContoh: test-phone 08123456789", contactId);
            return;
          }

          try {
            const validation = blastManager.validatePhoneNumber(phoneNumber);
            const testResult = await blastManager.testSendToNumber(phoneNumber, "🧪 Test message dari WhatsApp bot - abaikan saja");

            const resultMessage = `🧪 *TEST PHONE NUMBER*\n\n` +
              `📞 *Original:* ${validation.original}\n` +
              `🔧 *Cleaned:* ${validation.cleaned}\n` +
              `✅ *Valid:* ${validation.isValid ? 'Ya' : 'Tidak'}\n` +
              `🆔 *Contact ID:* ${validation.contactId || 'N/A'}\n\n` +
              `📤 *Send Test:* ${testResult.success ? '✅ Berhasil' : '❌ Gagal'}\n` +
              `${testResult.error ? `❌ Error: ${testResult.error}` : ''}`;

            await modeTyping(message, resultMessage, contactId);
          } catch (error) {
            await modeTyping(message, `❌ Error testing phone: ${error.message}`, contactId);
          }
          return;
        } else {
          await modeTyping(message, "🚫 Maaf, Anda tidak memiliki izin untuk test phone.", contactId);
          return;
        }
      }

      // Check for lead statistics command
      if (message.body.toLowerCase() === "stats-lead") {
        if (isAdminContact(contactId)) {
          const businessStats = businessDataManager.getStatistics();
          const leadStats = leadManager.getStatistics();

          const statsMessage = `📊 *STATISTIK BISNIS & LEAD*\n\n` +
            `*Data Bisnis:*\n` +
            `• Total: ${businessStats.total}\n` +
            `• Sudah dihubungi: ${businessStats.contacted}\n` +
            `• Belum dihubungi: ${businessStats.uncontacted}\n` +
            `• Tertarik: ${businessStats.interested}\n\n` +
            `*Lead Management:*\n` +
            `• Total lead: ${leadStats.total}\n` +
            `• Lead baru: ${leadStats.new}\n` +
            `• Tertarik: ${leadStats.interested}\n` +
            `• Qualified: ${leadStats.qualified}\n` +
            `• Closed: ${leadStats.closed}\n` +
            `• Conversion rate: ${leadStats.conversionRate}%\n\n` +
            `*Available Commands:*\n` +
            `• test-blast - Test blast functionality\n` +
            `• test-phone [nomor] - Test kirim ke nomor tertentu\n` +
            `• blast-bisnis - Blast ke semua bisnis\n` +
            `• stats-lead - Lihat statistik ini`;

          await modeTyping(message, statsMessage, contactId);
          return;
        }
      }

      await useTemplateMessageKawan(message, contact);
    }
  } catch (error) {
    console.log(`Error in saveMessage: ${error}`);
  }
}

async function useTemplateMessageKawan(message, contact) {
  try {
    console.log("Processing message with AI Assistant");

    // Get chat for typing indicators
    const chat = await message.getChat();
    const contactId = contact.id._serialized;

    // Check if this is a business responding to our blast
    const business = businessDataManager.findBusinessByPhone(contact.id.user);
    if (business && business.contacted) {
      await handleBusinessResponse(message, contact, business);
      return;
    }

    // Implement message batching
    await handleMessageBatching(message, contact);

  } catch (error) {
    console.log(`Error in message processing: ${error}`);

    // Send fallback message
    const fallbackMessage = "Maaf, terjadi kesalahan dalam memproses pesan Anda. Silakan coba lagi atau ketik 'admin' untuk berbicara dengan admin.";
    await modeTyping(message, fallbackMessage, contact.id._serialized);
  }
}

// Handle message batching to wait for multiple messages
async function handleMessageBatching(message, contact) {
  const contactId = contact.id._serialized;
  
  // Initialize batch for this contact if it doesn't exist
  if (!messageBatches.has(contactId)) {
    messageBatches.set(contactId, {
      messages: [],
      timeout: null,
      processing: false
    });
  }
  
  const batch = messageBatches.get(contactId);
  
  // If already processing, queue this message
  if (batch.processing) {
    batch.messages.push(message.body);
    return;
  }
  
  // Add message to batch
  batch.messages.push(message.body);
  
  // Clear existing timeout
  if (batch.timeout) {
    clearTimeout(batch.timeout);
  }
  
  // Set new timeout or process immediately if batch is full
  if (batch.messages.length >= MAX_BATCH_SIZE) {
    await processBatchedMessages(contactId, contact);
  } else {
    batch.timeout = setTimeout(async () => {
      await processBatchedMessages(contactId, contact);
    }, BATCH_TIMEOUT);
  }
}

// Process all batched messages for a contact
async function processBatchedMessages(contactId, contact) {
  const batch = messageBatches.get(contactId);
  if (!batch || batch.messages.length === 0) return;
  
  batch.processing = true;
  
  try {
    // Combine all messages in the batch
    const combinedMessage = batch.messages.join(' ');
    console.log(`Processing batched messages for ${contactId}: ${batch.messages.length} messages`);
    
    // Check for local/predefined responses first
    const localResponse = await cekLocalMessage(combinedMessage);
    
    let answer = "";
    
    if (localResponse) {
      answer = localResponse.message || localResponse.pesan || localResponse;
    } else {
      // Use AI Assistant for response with contact information
      answer = await runAiAssistant(combinedMessage, contactId, contact.name || contact.pushname);
    }
    
    // Send the response using typing mode
    await sendMessageSafely(contactId, answer);
    
  } catch (error) {
    console.log(`Error in batched message processing: ${error}`);
    
    // Send fallback message
    const fallbackMessage = "Maaf, terjadi kesalahan dalam memproses pesan Anda. Silakan coba lagi atau ketik 'admin' untuk berbicara dengan admin.";
    await sendMessageSafely(contactId, fallbackMessage);
  } finally {
    // Reset batch
    batch.messages = [];
    batch.processing = false;
    if (batch.timeout) {
      clearTimeout(batch.timeout);
      batch.timeout = null;
    }
  }
}

// Handle responses from businesses that received our blast
async function handleBusinessResponse(message, contact, business) {
  try {
    const messageText = message.body.toLowerCase();
    const contactId = contact.id._serialized;

    // Check for positive and negative responses
    const isPositive = POSITIVE_KEYWORDS.some(keyword => messageText.includes(keyword));
    const isNegative = NEGATIVE_KEYWORDS.some(keyword => messageText.includes(keyword));

    if (isPositive) {
      // Mark as interested and create lead
      businessDataManager.markAsInterested(business.id, message.body);
      leadManager.addLead(business.name, business.phone, message.body, 'whatsapp_blast_response');

      const response = `Wah, excellent choice ${business.name}! 🎉✨\n\n` +
        `Saya Renata sangat excited bisa membantu ${business.name} naik level! 🚀\n\n` +
        `*NEXT STEPS:*\n` +
        `1️⃣ Saya akan call Anda hari ini untuk konsultasi GRATIS\n` +
        `2️⃣ Analisis mendalam kebutuhan bisnis ${business.name}\n` +
        `3️⃣ Proposal solusi digital yang tepat sasaran\n` +
        `4️⃣ Demo live sistem yang akan boost omzet Anda!\n\n` +
        `📞 *Kontak langsung saya:*\n` +
        `WhatsApp: ${process.env.WHATSAPP_BOT_NUMBER}\n` +
        `(Renata - Digital Business Consultant)\n\n` +
        `💡 *Fun fact:* Client terakhir saya omzetnya naik 250% dalam 4 bulan setelah pakai sistem kami!\n\n` +
        `Siap-siap ${business.name} jadi market leader di industri Anda! 💪\n\n` +
        `Will call you soon! 📞✨`;

      await modeTyping(message, response, contactId);

      // Log for admin follow-up
      console.log(`🎯 NEW QUALIFIED LEAD: ${business.name} (${business.phone}) - ${message.body}`);
      console.log(`📞 URGENT: Call ${business.phone} today for follow-up!`);

    } else if (isNegative) {
      // Mark as not interested
      businessDataManager.markAsContacted(business.id, 'not_interested');

      const response = `Terima kasih atas responnya. Jika suatu saat ${business.name} membutuhkan layanan pengembangan website, sistem, atau analisis data, jangan ragu untuk menghubungi kami. Semoga bisnis Anda sukses! 🙏`;

      await modeTyping(message, response, contactId);

    } else {
      // Neutral response - be more persuasive
      const response = `Hi ${business.name}! 👋\n\n` +
        `Thanks for responding! Saya Renata, dan saya paham mungkin Anda masih consider ya? 😊\n\n` +
        `*Let me share quick success story:*\n` +
        `Kemarin client saya (toko fashion di Nagoya) omzetnya naik 300% dalam 3 bulan setelah pakai website + sistem inventory kami! 📈\n\n` +
        `*Yang bikin beda:*\n` +
        `🎯 Website yang convert visitor jadi buyer\n` +
        `📊 Sistem yang otomatis track semua (no manual lagi!)\n` +
        `💰 ROI terbukti - balik modal cepat\n\n` +
        `*Special untuk ${business.name}:*\n` +
        `Konsultasi GRATIS + analisis kompetitor senilai 500rb!\n\n` +
        `Cuma butuh 15 menit call untuk lihat potensi bisnis Anda bisa naik berapa persen 🚀\n\n` +
        `📞 *Direct contact:*\n` +
        `WhatsApp: ${process.env.WHATSAPP_BOT_NUMBER}\n\n` +
        `Balas "tertarik" untuk slot konsultasi gratis, atau "tidak" jika memang tidak butuh saat ini.\n\n` +
        `No pressure, tapi opportunity ini limited ya! 😉`;

      await modeTyping(message, response, contactId);
    }

  } catch (error) {
    console.error('Error handling business response:', error);
  }
}

// Initialize client with retry logic
let initializationAttempts = 0;
const maxRetries = 3;

async function initializeClientWithRetry() {
  try {
    initializationAttempts++;
    console.log(`🔄 Initializing WhatsApp client (attempt ${initializationAttempts}/${maxRetries})...`);
    console.log("⏳ This may take a few moments while Chrome is being launched...");

    await client.initialize();
    console.log("✅ Client initialization completed successfully!");
  } catch (error) {
    console.error(`❌ Initialization attempt ${initializationAttempts} failed:`, error.message);

    if (initializationAttempts < maxRetries) {
      console.log(`🔄 Retrying in 10 seconds...`);
      setTimeout(() => {
        initializeClientWithRetry();
      }, 10000);
    } else {
      console.error("💥 Max initialization attempts reached. Please check your configuration and try again.");
      process.exit(1);
    }
  }
}

console.log("🚀 Starting WhatsApp client initialization...");
// Start the initialization
initializeClientWithRetry();
