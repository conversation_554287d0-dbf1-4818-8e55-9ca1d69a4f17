/**
 * Persistent Persuasion System
 * Implements never-accept-no strategies and advanced objection handling
 */

class PersistentPersuasion {
    constructor() {
        // Never-accept-no strategies
        this.REJECTION_RESPONSES = {
            direct_no: [
                "I understand. What specifically concerns you about moving forward?",
                "That's fair. What would need to change for this to make sense?",
                "I hear you. What's the main thing holding you back right now?",
                "No problem. Can you help me understand what's not quite right?",
                "I appreciate your honesty. What would make this a no-brainer for you?"
            ],
            not_interested: [
                "I get it. What would you need to see to spark your interest?",
                "That's okay. What kind of solution would interest you?",
                "Fair enough. What's your biggest business challenge right now?",
                "I understand. What would make you excited about a business solution?",
                "No worries. What would need to happen for you to be interested?"
            ],
            too_busy: [
                "I totally understand you're swamped. What if this could save you 10+ hours per week?",
                "Being busy is exactly why automation is so valuable. What takes up most of your time?",
                "I hear you. What if I could show you how to get those hours back?",
                "Busy is good - means business is growing! What's eating up your time?",
                "That's why successful people invest in systems. What's your biggest time drain?"
            ],
            no_budget: [
                "Budget is important. What's the cost of NOT solving this problem?",
                "I understand. What would the ROI need to be for this to make sense?",
                "Fair point. What if this paid for itself in the first month?",
                "Budget-wise, what would make this an easy yes?",
                "I get it. What if I could show you how this actually saves money?"
            ],
            need_to_think: [
                "Absolutely, smart to think it through. What specific aspects are you considering?",
                "Of course! What questions can I answer to help your decision?",
                "That's wise. What information would be most helpful?",
                "Smart approach. What would you need to know to feel confident?",
                "Good idea. What factors are most important in your decision?"
            ]
        };

        // Follow-up sequences for different scenarios
        this.FOLLOW_UP_SEQUENCES = {
            silent_after_interest: [
                "Just wanted to make sure you got my last message about the solution we discussed. Any thoughts?",
                "I know you're probably busy, but I had another idea for your business that might interest you...",
                "Quick question - what's the biggest challenge you're facing in your business right now?",
                "I came across something that reminded me of our conversation. Mind if I share a quick insight?"
            ],
            objection_not_resolved: [
                "I've been thinking about your concern regarding [objection]. Here's another perspective...",
                "You mentioned [objection] - I have a case study that might address that exact issue.",
                "I understand your hesitation about [objection]. What if I could show you how others overcame this?",
                "Your point about [objection] is valid. Let me share how we've solved this for similar businesses."
            ],
            high_interest_no_action: [
                "You seemed excited about the potential results. What's preventing you from moving forward?",
                "I know you saw the value in this solution. What would need to happen for you to get started?",
                "You mentioned this could transform your business. What's the holdup?",
                "I can tell you're interested. What's the one thing that would make this an easy decision?"
            ]
        };

        // Closing techniques
        this.CLOSING_TECHNIQUES = {
            alternative: [
                "Would you prefer to start with the basic package or go with the full solution?",
                "Should we schedule a 30-minute or 60-minute consultation?",
                "Would this week or next week work better for implementation?",
                "Do you want to focus on the efficiency gains or revenue growth first?",
                "Would you like the technical demo or business case presentation?"
            ],
            assumptive: [
                "When would be the best time for Renata to call you this week?",
                "I'll have Renata prepare a custom proposal. What's your email?",
                "Let me check Renata's calendar for your consultation slot.",
                "I'll set up a priority consultation. What's your preferred time?",
                "Renata will want to understand your specific needs. When can she reach you?"
            ],
            urgency: [
                "I only have 2 consultation slots left this month. Should I reserve one for you?",
                "This special implementation rate expires next week. Interested in locking it in?",
                "We're only taking 3 new clients this quarter. Want to secure your spot?",
                "The development team has limited capacity. Should I add you to the priority list?",
                "Early bird pricing ends soon. Would you like me to hold that rate for you?"
            ],
            fear_of_loss: [
                "While you're thinking about it, your competitors might be implementing similar solutions.",
                "Every day without this system is lost revenue. How much is that costing you?",
                "The longer you wait, the further behind you fall. Ready to take the lead?",
                "Your competition isn't waiting. Shouldn't you stay ahead?",
                "Time is money, and this system saves both. What's the delay costing you?"
            ]
        };

        // Objection prevention statements
        this.OBJECTION_PREVENTION = {
            price: "Before you think about cost, let me show you the value...",
            time: "I know you're busy, that's exactly why this automation is perfect...",
            trust: "I understand you need to be careful with business decisions...",
            authority: "Smart business owners always involve their team in decisions like this..."
        };

        // Re-engagement triggers
        this.RE_ENGAGEMENT_TRIGGERS = [
            "I just helped a business similar to yours increase revenue by 300%. Curious how?",
            "Quick question: What's your biggest business frustration right now?",
            "I have a 5-minute insight that could save you hours. Interested?",
            "Just saw something that could solve your [specific problem]. Want to hear about it?",
            "I'm working with a client who had the exact same challenge as you..."
        ];
    }

    /**
     * Detect rejection type from message
     */
    detectRejectionType(message) {
        const lowerMessage = message.toLowerCase();
        
        if (lowerMessage.includes('no') || lowerMessage.includes('tidak')) {
            return 'direct_no';
        }
        if (lowerMessage.includes('not interested') || lowerMessage.includes('tidak tertarik')) {
            return 'not_interested';
        }
        if (lowerMessage.includes('busy') || lowerMessage.includes('sibuk')) {
            return 'too_busy';
        }
        if (lowerMessage.includes('budget') || lowerMessage.includes('mahal') || lowerMessage.includes('expensive')) {
            return 'no_budget';
        }
        if (lowerMessage.includes('think') || lowerMessage.includes('pikir') || lowerMessage.includes('consider')) {
            return 'need_to_think';
        }
        
        return null;
    }

    /**
     * Generate response to rejection that doesn't accept no
     */
    handleRejection(rejectionType, context = {}) {
        if (!rejectionType || !this.REJECTION_RESPONSES[rejectionType]) {
            return this.REJECTION_RESPONSES.direct_no[0];
        }
        
        const responses = this.REJECTION_RESPONSES[rejectionType];
        const randomResponse = responses[Math.floor(Math.random() * responses.length)];
        
        return {
            response: randomResponse,
            followUpStrategy: this.getFollowUpStrategy(rejectionType, context),
            nextTechnique: this.getNextPersuasionTechnique(rejectionType)
        };
    }

    /**
     * Get follow-up strategy for specific rejection
     */
    getFollowUpStrategy(rejectionType, context) {
        const strategies = {
            direct_no: 'Dig deeper into the real objection behind the no',
            not_interested: 'Find what would create interest',
            too_busy: 'Position as time-saving solution',
            no_budget: 'Focus on ROI and cost of inaction',
            need_to_think: 'Provide information to aid decision'
        };
        
        return strategies[rejectionType] || 'Continue building value';
    }

    /**
     * Get next persuasion technique to try
     */
    getNextPersuasionTechnique(rejectionType) {
        const techniques = {
            direct_no: 'social_proof',
            not_interested: 'curiosity_gap',
            too_busy: 'time_value',
            no_budget: 'roi_focus',
            need_to_think: 'risk_reversal'
        };
        
        return techniques[rejectionType] || 'rapport_building';
    }

    /**
     * Generate closing attempt based on conversation readiness
     */
    generateClosingAttempt(readinessLevel, technique = 'alternative') {
        if (!this.CLOSING_TECHNIQUES[technique]) {
            technique = 'alternative';
        }
        
        const closes = this.CLOSING_TECHNIQUES[technique];
        const selectedClose = closes[Math.floor(Math.random() * closes.length)];
        
        // Add urgency for high readiness
        if (readinessLevel > 70 && technique !== 'urgency') {
            const urgencyClose = this.CLOSING_TECHNIQUES.urgency[Math.floor(Math.random() * this.CLOSING_TECHNIQUES.urgency.length)];
            return `${selectedClose}\n\n⚡ ${urgencyClose}`;
        }
        
        return selectedClose;
    }

    /**
     * Generate re-engagement message for silent prospects
     */
    generateReEngagement(daysSilent, lastInteractionType = 'general') {
        let sequence;
        
        if (lastInteractionType === 'high_interest') {
            sequence = this.FOLLOW_UP_SEQUENCES.high_interest_no_action;
        } else if (lastInteractionType === 'objection') {
            sequence = this.FOLLOW_UP_SEQUENCES.objection_not_resolved;
        } else {
            sequence = this.FOLLOW_UP_SEQUENCES.silent_after_interest;
        }
        
        // Select message based on days silent
        let messageIndex = Math.min(Math.floor(daysSilent / 2), sequence.length - 1);
        let message = sequence[messageIndex];
        
        // Add re-engagement trigger for very silent prospects
        if (daysSilent > 7) {
            const trigger = this.RE_ENGAGEMENT_TRIGGERS[Math.floor(Math.random() * this.RE_ENGAGEMENT_TRIGGERS.length)];
            message = trigger;
        }
        
        return {
            message: message,
            technique: this.getReEngagementTechnique(daysSilent),
            urgency: daysSilent > 5 ? 'high' : 'medium'
        };
    }

    /**
     * Get re-engagement technique based on silence duration
     */
    getReEngagementTechnique(daysSilent) {
        if (daysSilent <= 2) return 'gentle_follow_up';
        if (daysSilent <= 5) return 'value_add';
        if (daysSilent <= 10) return 'curiosity_gap';
        return 'fresh_start';
    }

    /**
     * Prevent objections before they arise
     */
    preventObjection(likelyObjection) {
        return this.OBJECTION_PREVENTION[likelyObjection] || 
               "Let me address what most business owners ask about...";
    }

    /**
     * Create multiple touchpoints strategy
     */
    createTouchpointSequence(prospectProfile) {
        const sequence = [];
        
        // Day 1: Initial follow-up
        sequence.push({
            day: 1,
            message: "Just wanted to make sure you got my message. Any initial thoughts?",
            technique: 'gentle_follow_up'
        });
        
        // Day 3: Value-add
        sequence.push({
            day: 3,
            message: "I had another idea for your business that might interest you...",
            technique: 'value_add'
        });
        
        // Day 7: Social proof
        sequence.push({
            day: 7,
            message: "Just helped a similar business achieve amazing results. Want to hear about it?",
            technique: 'social_proof'
        });
        
        // Day 14: Fresh perspective
        sequence.push({
            day: 14,
            message: "Quick question: What's your biggest business challenge right now?",
            technique: 'fresh_start'
        });
        
        // Day 30: Final attempt
        sequence.push({
            day: 30,
            message: "I know timing wasn't right before. Has anything changed in your business?",
            technique: 'timing_check'
        });
        
        return sequence;
    }

    /**
     * Analyze conversation for persuasion opportunities
     */
    analyzePersuasionOpportunities(conversation) {
        const opportunities = {
            objectionHandling: [],
            closingOpportunities: [],
            reEngagementNeeded: false,
            recommendedTechnique: null
        };
        
        // Check for unhandled objections
        conversation.objectionPatterns.forEach(objection => {
            opportunities.objectionHandling.push({
                type: objection.type,
                technique: this.getNextPersuasionTechnique(objection.type)
            });
        });
        
        // Check for closing opportunities
        if (conversation.conversionReadiness > 60) {
            opportunities.closingOpportunities.push('alternative_close');
        }
        if (conversation.conversionReadiness > 80) {
            opportunities.closingOpportunities.push('assumptive_close');
        }
        
        // Check if re-engagement is needed
        opportunities.reEngagementNeeded = conversation.daysSinceLastMessage > 1;
        
        // Recommend primary technique
        opportunities.recommendedTechnique = this.getRecommendedTechnique(conversation);
        
        return opportunities;
    }

    /**
     * Get recommended persuasion technique for current situation
     */
    getRecommendedTechnique(conversation) {
        if (conversation.conversionReadiness > 80) return 'assumptive_close';
        if (conversation.conversionReadiness > 60) return 'alternative_close';
        if (conversation.objectionCount > 2) return 'objection_prevention';
        if (conversation.engagementLevel < 50) return 're_engagement';
        if (conversation.daysSinceLastMessage > 3) return 'follow_up_sequence';
        
        return 'value_building';
    }
}

module.exports = PersistentPersuasion;
