const BusinessDataManager = require('./src/business_data');
const BlastManager = require('./src/blast_manager');
const fs = require('fs');
const dotenv = require('dotenv');
dotenv.config();

class TestBlastManager extends BusinessDataManager {
    constructor() {
        super();
        this.businessDataFile = 'test_business_data.json';
        this.loadBusinessData();
    }

    // Override to use test data
    loadBusinessData() {
        try {
            if (fs.existsSync(this.businessDataFile)) {
                const data = fs.readFileSync(this.businessDataFile, 'utf8');
                this.businessData = JSON.parse(data);
                console.log(`📊 Test data loaded: ${this.businessData.length} test businesses`);
                return this.businessData;
            } else {
                console.log('❌ Test business data file not found');
                return [];
            }
        } catch (error) {
            console.error('Error loading test business data:', error);
            return [];
        }
    }

    // Reset test business to uncontacted state for testing
    resetTestBusinessForTesting() {
        this.businessData.forEach(business => {
            business.contacted = false;
            business.contactDate = null;
            business.response = null;
            business.interested = false;
        });
        this.saveBusinessData();
        console.log('🔄 Test business reset to uncontacted state');
    }
}

// Function to run test blast
async function runTestBlast(whatsappClient) {
    console.log('🧪 Starting TEST BLAST...');
    console.log(`📱 Target: ${process.env.TESTING_PHONE_NUMBER} (Test Business Renata)`);

    const testBusinessManager = new TestBlastManager();

    // Reset test business to uncontacted state before testing
    testBusinessManager.resetTestBusinessForTesting();

    const testBlastManager = new BlastManager(whatsappClient);

    // Override the business data manager in blast manager
    testBlastManager.businessDataManager = testBusinessManager;
    
    try {
        const result = await testBlastManager.sendBlast('complete_package', {
            batchSize: 1,
            delayBetweenMessages: 2000,
            delayBetweenBatches: 5000
        });
        
        console.log('🎉 Test blast completed!');
        console.log(`✅ Sent: ${result.sent}`);
        console.log(`❌ Failed: ${result.failed}`);
        
        return result;
    } catch (error) {
        console.error('❌ Test blast failed:', error);
        throw error;
    }
}

module.exports = { runTestBlast, TestBlastManager };
