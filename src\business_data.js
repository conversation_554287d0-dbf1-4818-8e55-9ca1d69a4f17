const XLSX = require('xlsx');
const fs = require('fs');
const path = require('path');

class BusinessDataManager {
    constructor() {
        this.businessDataFile = 'business_data.json';
        this.excelFile = 'filtered_business_data_no_chains_updated.xlsx';
        this.businessData = [];
    }

    /**
     * Convert Excel file to JSON format
     */
    async convertExcelToJson() {
        try {
            console.log('Converting Excel file to JSON...');
            
            // Read the Excel file
            const workbook = XLSX.readFile(this.excelFile);
            const sheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[sheetName];
            
            // Convert to JSON
            const jsonData = XLSX.utils.sheet_to_json(worksheet);
            
            // Process and clean the data
            const processedData = jsonData.map((row, index) => {
                return {
                    id: index + 1,
                    name: row.name || row.Name || row.NAMA || '',
                    latitude: row.latitude || row.Latitude || row.LAT || '',
                    longitude: row.longitude || row.Longitude || row.LNG || '',
                    address: row.address || row.Address || row.ALAMAT || '',
                    phone: this.cleanPhoneNumber(row.phone || row.Phone || row.TELEPON || ''),
                    website: row.website || row.Website || row.WEBSITE || '',
                    category: row.category || row.Category || row.KATEGORI || '',
                    link: row.link || row.Link || row.LINK || '',
                    contacted: false,
                    contactDate: null,
                    response: null,
                    interested: false
                };
            }).filter(business => business.name && business.phone); // Only include businesses with name and phone

            // Save to JSON file
            fs.writeFileSync(this.businessDataFile, JSON.stringify(processedData, null, 2));
            
            console.log(`Successfully converted ${processedData.length} business records to JSON`);
            this.businessData = processedData;
            
            return processedData;
        } catch (error) {
            console.error('Error converting Excel to JSON:', error);
            throw error;
        }
    }

    /**
     * Clean and format phone numbers for WhatsApp compatibility
     */
    cleanPhoneNumber(phone) {
        if (!phone) return '';

        // Remove all non-numeric characters
        let cleaned = phone.toString().replace(/\D/g, '');

        // Handle Indonesian phone numbers
        if (cleaned.startsWith('0')) {
            // Convert local format (08xx) to international (628xx)
            cleaned = '62' + cleaned.substring(1);
        } else if (cleaned.startsWith('8')) {
            // Add country code for numbers starting with 8
            cleaned = '62' + cleaned;
        } else if (!cleaned.startsWith('62')) {
            // Add country code if missing
            cleaned = '62' + cleaned;
        }

        // Validate Indonesian mobile number format
        if (cleaned.startsWith('62')) {
            // Indonesian mobile numbers should be 62 + 8xx + 8-9 more digits
            if (cleaned.length >= 11 && cleaned.length <= 15 && cleaned.substring(2, 3) === '8') {
                return cleaned;
            }
        }

        console.warn(`⚠️ Potentially invalid phone number format: ${phone} -> ${cleaned}`);
        return cleaned; // Return anyway, let WhatsApp handle validation
    }

    /**
     * Load business data from JSON file
     */
    loadBusinessData() {
        try {
            if (fs.existsSync(this.businessDataFile)) {
                const data = fs.readFileSync(this.businessDataFile, 'utf8');
                this.businessData = JSON.parse(data);
                return this.businessData;
            } else {
                console.log('Business data file not found. Converting Excel file...');
                return this.convertExcelToJson();
            }
        } catch (error) {
            console.error('Error loading business data:', error);
            return [];
        }
    }

    /**
     * Get all businesses
     */
    getAllBusinesses() {
        if (this.businessData.length === 0) {
            this.loadBusinessData();
        }
        return this.businessData;
    }

    /**
     * Get businesses that haven't been contacted yet
     */
    getUncontactedBusinesses() {
        return this.getAllBusinesses().filter(business => !business.contacted);
    }

    /**
     * Get businesses that showed interest
     */
    getInterestedBusinesses() {
        return this.getAllBusinesses().filter(business => business.interested);
    }

    /**
     * Mark a business as contacted
     */
    markAsContacted(businessId, response = null) {
        const business = this.businessData.find(b => b.id === businessId);
        if (business) {
            business.contacted = true;
            business.contactDate = new Date().toISOString();
            business.response = response;
            this.saveBusinessData();
        }
    }

    /**
     * Mark a business as interested
     */
    markAsInterested(businessId, response = null) {
        const business = this.businessData.find(b => b.id === businessId);
        if (business) {
            business.interested = true;
            business.response = response;
            this.saveBusinessData();
        }
    }

    /**
     * Find business by phone number
     */
    findBusinessByPhone(phone) {
        const cleanedPhone = this.cleanPhoneNumber(phone);
        return this.businessData.find(b => b.phone === cleanedPhone);
    }

    /**
     * Save business data to JSON file
     */
    saveBusinessData() {
        try {
            fs.writeFileSync(this.businessDataFile, JSON.stringify(this.businessData, null, 2));
        } catch (error) {
            console.error('Error saving business data:', error);
        }
    }

    /**
     * Get statistics
     */
    getStatistics() {
        const total = this.businessData.length;
        const contacted = this.businessData.filter(b => b.contacted).length;
        const interested = this.businessData.filter(b => b.interested).length;
        
        return {
            total,
            contacted,
            uncontacted: total - contacted,
            interested,
            conversionRate: contacted > 0 ? ((interested / contacted) * 100).toFixed(2) : 0
        };
    }
}

module.exports = BusinessDataManager;
