const dotenv = require('dotenv');
const axios = require('axios');
const ConversationManager = require('./conversation_manager');
const PsychologicalEngagement = require('./psychological_engagement');
const PersistentPersuasion = require('./persistent_persuasion');
const AdvancedCTAEngine = require('./advanced_cta_engine');
const ZeroClientLoss = require('./zero_client_loss');

dotenv.config();

// Google Gemini API Configuration
const GEMINI_API_CONFIG = {
    baseURL: 'https://generativelanguage.googleapis.com/v1beta',
    model: 'gemini-1.5-flash', // Free model with high quota
    headers: {
        'Content-Type': 'application/json'
    }
};
const conversationManager = new ConversationManager();
const psychEngine = new PsychologicalEngagement();
const persuasionEngine = new PersistentPersuasion();
const ctaEngine = new AdvancedCTAEngine();
const zeroLossEngine = new ZeroClientLoss();

// AI Service Configuration
const AI_CONFIG = {
    maxRetries: 3,
    baseDelay: 1000, // 1 second
    maxDelay: 30000, // 30 seconds
    backoffMultiplier: 2,
    retryableErrors: [
        'Service Unavailable',
        'overloaded',
        'rate limit',
        'timeout',
        'ECONNRESET',
        'ETIMEDOUT',
        'ENOTFOUND'
    ]
};

// ENHANCED Content filtering keywords for inappropriate topics with AUTO-BLOCK
const INAPPROPRIATE_KEYWORDS = {
    political: ['politik', 'political', 'pemilu', 'pilpres', 'pilkada', 'partai', 'pemerintah', 'presiden', 'menteri'],
    
    // COMPREHENSIVE SEXUAL CONTENT DETECTION (AUTO-BLOCK)
    sexual: [
        // Basic sexual terms
        'sex', 'seks', 'porn', 'porno', 'telanjang', 'bugil', 'xxx', 'dewasa', 'adult',
        // Explicit content
        'masturbasi', 'masturbation', 'orgasm', 'orgasme', 'cumshot', 'blowjob', 'anal',
        'vagina', 'penis', 'payudara', 'breast', 'nipple', 'puting', 'kelamin', 'genital',
        // Sexual activities
        'bercinta', 'making love', 'foreplay', 'oral sex', 'threesome', 'gangbang',
        'prostitusi', 'prostitution', 'pelacur', 'whore', 'escort', 'gigolo',
        // Pornographic terms
        'bokep', 'jav', 'milf', 'teen porn', 'hardcore', 'softcore', 'nude', 'naked',
        'strip', 'striptease', 'cam girl', 'onlyfans', 'webcam', 'live sex',
        // Sexual harassment terms
        'sexual harassment', 'pelecehan seksual', 'rape', 'perkosa', 'molest',
        // Body parts (sexual context)
        'boobs', 'tits', 'ass', 'butt', 'pantat', 'toket', 'memek', 'kontol',
        // Dating/hookup apps context
        'hookup', 'one night stand', 'fwb', 'friends with benefits', 'sugar daddy',
        // LGBTQ+ inappropriate usage
        'gay sex', 'lesbian sex', 'transgender porn'
    ],
    
    // COMPREHENSIVE THREAT DETECTION (AUTO-BLOCK)
    threats: [
        // Direct threats
        'bunuh', 'kill', 'mati', 'death', 'pembunuhan', 'murder', 'assassinate',
        'ancam', 'threaten', 'threat', 'intimidasi', 'intimidate',
        // Violence
        'kekerasan', 'violence', 'pukul', 'hit', 'beat', 'assault', 'attack',
        'tusuk', 'stab', 'tembak', 'shoot', 'bomb', 'bom', 'ledak', 'explode',
        // Harm intentions
        'sakiti', 'hurt', 'harm', 'damage', 'destroy', 'hancurkan',
        'torture', 'siksa', 'abuse', 'kekerasan', 'brutal',
        // Weapons
        'pistol', 'gun', 'rifle', 'knife', 'pisau', 'sword', 'pedang',
        'grenade', 'granat', 'explosive', 'bahan peledak',
        // Terrorism related
        'terrorist', 'teroris', 'jihad', 'suicide bomb', 'bom bunuh diri',
        // Self-harm
        'suicide', 'bunuh diri', 'self harm', 'cutting', 'overdose',
        // Kidnapping
        'kidnap', 'culik', 'penculikan', 'ransom', 'tebusan'
    ],
    
    // HARASSMENT & HATE SPEECH (AUTO-BLOCK)
    harassment: [
        'racist', 'rasis', 'discrimination', 'diskriminasi', 'hate speech',
        'cyberbullying', 'bullying', 'stalking', 'menguntit',
        'blackmail', 'pemerasan', 'extortion', 'ancaman',
        'doxxing', 'revenge porn', 'sextortion'
    ],
    
    violence: ['perang', 'war', 'fight', 'berkelahi', 'violent', 'aggressive'],
    drugs: ['narkoba', 'drugs', 'ganja', 'marijuana', 'cocaine', 'heroin', 'sabu', 'ecstasy', 'methamphetamine'],
    gambling: ['judi', 'gambling', 'casino', 'taruhan', 'betting', 'togel', 'slot']
};

// AUTO-BLOCK SEVERITY LEVELS
const BLOCK_SEVERITY = {
    IMMEDIATE_BLOCK: ['sexual', 'threats', 'harassment'], // These categories trigger immediate blocking
    WARNING_REDIRECT: ['political', 'violence', 'drugs', 'gambling'] // These get professional redirect
};

// Short dynamic greetings based on context
const DYNAMIC_GREETINGS = {
    retail: ['Hi! 👋 Toko online ya? Saya Renata, Web Developer & Data Scientist! 🚀 renatahenessa.com', 'Halo! 👋 Bisnis retail? Saya Renata, spesialis e-commerce & dashboard! 🛍️ renatahenessa.com'],
    manufacturing: ['Halo! 👋 Pabrik/manufaktur? Saya Renata, spesialis sistem automasi! ⚙️ renatahenessa.com', 'Hi! 👋 Industri ya? Saya Renata, bisa bantu digitalisasi proses! 🏭 renatahenessa.com'],
    restaurant: ['Hi! 👋 Bisnis F&B? Saya Renata, bisa bantu sistem POS & inventory! 🍽️ renatahenessa.com', 'Halo! 👋 Restoran/cafe? Saya Renata, spesialis sistem kasir! ☕ renatahenessa.com'],
    tech: ['Hello! 👋 Tech startup? Saya Renata, Data Scientist & Web Developer! 💻 renatahenessa.com', 'Hi! 👋 Butuh development? Saya Renata, Full-stack Developer! 🚀 renatahenessa.com'],
    government: ['Halo! 👋 Instansi pemerintah? Saya Renata, spesialis digitalisasi! 🏛️ renatahenessa.com', 'Hi! 👋 Sektor publik? Saya Renata, bisa bantu sistem informasi! 📊 renatahenessa.com'],
    corporate: ['Hello! 👋 Enterprise solution? Saya Renata, spesialis business intelligence! 🏢 renatahenessa.com', 'Hi! 👋 Korporat? Saya Renata, bisa bantu dashboard analytics! 📈 renatahenessa.com'],
    general: ['Hi! 👋 Saya Renata, Web Developer & Data Scientist! ✨ renatahenessa.com', 'Halo! 👋 Saya Renata, spesialis solusi digital bisnis! 💡 renatahenessa.com']
};

// Context-specific follow-up questions
const CONTEXT_QUESTIONS = {
    retail: ['Apa challenge terbesar di toko online Anda?', 'Sistem inventory sudah terintegrasi?'],
    manufacturing: ['Proses mana yang paling butuh otomasi?', 'Sudah ada sistem tracking produksi?'],
    restaurant: ['Sistem kasir & inventory sudah terintegrasi?', 'Gimana cara track pesanan sekarang?'],
    tech: ['Butuh scaling system atau new development?', 'Tech stack apa yang digunakan sekarang?'],
    government: ['Digitalisasi proses apa yang jadi prioritas?', 'Sudah ada sistem informasi terintegrasi?'],
    corporate: ['System integration atau BI yang dibutuhkan?', 'Bagaimana data flow antar departemen?'],
    general: ['Cerita dong, bisnis apa yang Anda jalankan?', 'Apa tantangan utama bisnis Anda saat ini?']
};

// Business problem inquiry templates - SHORT for Indonesian users
const BUSINESS_INQUIRY_TEMPLATES = [
    'Pencatatan bisnis masih manual? 📊',
    'Susah tracking stok/pesanan? 🔍',
    'Laporan bisnis lama buatnya? ⏱️',
    'Data pelanggan berantakan? 📱',
    'Gimana monitor performa bisnis? 📈'
];

// Portfolio showcase templates
const PORTFOLIO_TEMPLATES = [
    `📊 Data Analytics: Dashboard bisnis, laporan otomatis, prediksi penjualan`,
    `📈 Web Development: E-commerce, sistem inventory, integrasi payment`,
    `💡 Business Intelligence: Visualisasi data, tracking performa, automasi`
];

// Business-related keywords that should be encouraged
const BUSINESS_KEYWORDS = {
    system_types: ['supplier', 'gudang', 'toko', 'kasir', 'inventory', 'stok', 'order', 'pesanan', 'whatsapp', 'wa', 'chatbot', 'cs'],
    business_needs: ['otomatis', 'automasi', 'integrasi', 'sistem', 'aplikasi', 'website', 'digital', 'online', 'tracking', 'monitor', 'laporan', 'analisis'],
    pain_points: ['manual', 'lama', 'susah', 'ribet', 'error', 'salah', 'telat', 'lambat', 'bingung', 'repot', 'sibuk', 'ketinggalan'],
    improvement_goals: ['cepat', 'mudah', 'praktis', 'efisien', 'akurat', 'hemat', 'berkembang', 'maju', 'modern', 'profesional']
};

/**
 * Call Google Gemini API with the given prompt
 */
async function callGeminiAPI(comprehensivePrompt, conversation = null) {
    try {
        const apiKey = process.env.AI_API_KEY;
        if (!apiKey) {
            throw new Error('AI_API_KEY not found in environment variables');
        }

        const url = `${GEMINI_API_CONFIG.baseURL}/models/${GEMINI_API_CONFIG.model}:generateContent?key=${apiKey}`;
        
        const response = await axios.post(url, {
            contents: [{
                parts: [{
                    text: comprehensivePrompt
                }]
            }],
            generationConfig: {
                temperature: 0.7,
                topK: 40,
                topP: 0.95,
                maxOutputTokens: 150,
                stopSequences: []
            },
            safetySettings: [
                {
                    category: "HARM_CATEGORY_HARASSMENT",
                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    category: "HARM_CATEGORY_HATE_SPEECH",
                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    category: "HARM_CATEGORY_DANGEROUS_CONTENT",
                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                }
            ]
        }, {
            headers: GEMINI_API_CONFIG.headers,
            timeout: 30000 // 30 second timeout
        });

        // Process the response
        if (response.data && response.data.candidates && response.data.candidates[0]) {
            const candidate = response.data.candidates[0];
            
            // Check if content was blocked by safety filters
            if (candidate.finishReason === 'SAFETY') {
                console.log('🛡️ Gemini blocked content due to safety filters');
                throw new Error('Content blocked by safety filters');
            }
            
            if (candidate.content && candidate.content.parts && candidate.content.parts[0]) {
                let aiResponse = candidate.content.parts[0].text;
                return aiResponse;
            } else {
                throw new Error('Invalid response format from Gemini API');
            }
        } else {
            throw new Error('No valid candidates in Gemini API response');
        }
    } catch (error) {
        console.error('Gemini API Error:', error.message);

        // Enhanced error handling for different scenarios
        if (error.response?.status === 403) {
            const errorData = error.response.data;
            console.log('🚨 Gemini API 403 Error Details:', errorData);
            throw new Error('Gemini API access denied - check API key');
        }

        // Handle quota exceeded
        if (error.response?.status === 429) {
            console.log('⏰ Gemini API quota exceeded - will retry with backoff');
            throw new Error('Quota exceeded');
        }

        // Handle server errors
        if (error.response?.status >= 500) {
            console.log('🔧 Gemini API server error - will retry');
            throw new Error('Server error');
        }

        // Handle safety filter blocks
        if (error.message.includes('safety filters') || error.message.includes('Content blocked')) {
            throw new Error('Content blocked by safety filters');
        }

        throw error;
    }
}

/**
 * Sleep utility for retry delays
 */
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Check if error is retryable
 */
function isRetryableError(error) {
    if (!error) return false;

    const errorMessage = error.message || error.toString();

    // Don't retry API key errors - these need manual intervention
    if (errorMessage.includes('access denied') ||
        errorMessage.includes('check API key') ||
        errorMessage.includes('safety filters') ||
        errorMessage.includes('Content blocked')) {
        return false;
    }

    // Retry server errors and quota limits
    if (errorMessage.includes('Quota exceeded') ||
        errorMessage.includes('Server error') ||
        errorMessage.includes('timeout') ||
        errorMessage.includes('network')) {
        return true;
    }

    return AI_CONFIG.retryableErrors.some(retryableError =>
        errorMessage.toLowerCase().includes(retryableError.toLowerCase())
    );
}

/**
 * Calculate delay for exponential backoff
 */
function calculateDelay(attempt) {
    const delay = AI_CONFIG.baseDelay * Math.pow(AI_CONFIG.backoffMultiplier, attempt);
    return Math.min(delay, AI_CONFIG.maxDelay);
}

// Legacy generateFallbackResponse function removed - now using AI-first approach

// Legacy generateContextualFallbackResponse function removed - now using AI-first approach

/**
 * Retry AI service call with exponential backoff
 */
async function retryAIServiceCall(aiFunction, maxRetries = AI_CONFIG.maxRetries) {
    let lastError;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
        try {
            console.log(`🤖 AI Service attempt ${attempt + 1}/${maxRetries + 1}`);
            const result = await aiFunction();
            console.log(`✅ AI Service call successful on attempt ${attempt + 1}`);
            return result;
        } catch (error) {
            lastError = error;
            console.error(`❌ AI Service attempt ${attempt + 1} failed:`, error.message);

            // If this is the last attempt or error is not retryable, throw
            if (attempt === maxRetries || !isRetryableError(error)) {
                console.error(`💥 AI Service failed after ${attempt + 1} attempts. Last error:`, error.message);
                throw error;
            }

            // Calculate delay and wait before retry
            const delay = calculateDelay(attempt);
            console.log(`⏳ Waiting ${delay}ms before retry...`);
            await sleep(delay);
        }
    }

    throw lastError;
}

// Professional portfolio and credentials with real achievements
const PORTFOLIO_INFO = {
    website: "renatahenessa.com",
    credentials: {
        education: "BSc in Data Science dari STIS Statistical Polytechnic",
        certification: "Associate Data Scientist bersertifikat (skor 89.89/100)",
        business_projects: "Multiple startup dan government projects",
        entrepreneurship: "Multiple startup experience di bidang digital commerce"
    },
    technical_expertise: {
        data_science: "Python, R, SQL untuk analisis bisnis dan dashboard",
        web_development: "Laravel, JavaScript untuk e-commerce dan sistem bisnis",
        business_intelligence: "Dashboard analytics dan reporting systems",
        system_integration: "API development dan business automation"
    },
    measurable_results: {
        cost_reduction: "100% pengurangan biaya operasional (proyek terdahulu)",
        time_efficiency: "88% pengurangan waktu proses melalui otomasi",
        operational_improvement: "60% peningkatan efisiensi operasional secara keseluruhan",
        project_success: "Hasil terukur dan quantifiable di setiap implementasi"
    },
    specialties: [
        "Data Science & Business Analytics",
        "Full-stack web development dengan Laravel & JavaScript",
        "Business Intelligence & Dashboard analytics",
        "E-commerce solutions & payment integration",
        "Digital commerce solutions",
        "Business process automation"
    ]
};

/**
 * Detect business context from user message
 */
function detectBusinessContext(message) {
    const lowerMessage = message.toLowerCase();
    
    const keywords = {
        retail: ['toko', 'online', 'ecommerce', 'jual', 'produk', 'barang', 'inventory', 'stok', 'marketplace'],
        manufacturing: ['pabrik', 'manufaktur', 'produksi', 'mesin', 'otomasi', 'industri', 'warehouse', 'gudang'],
        restaurant: ['restoran', 'cafe', 'makanan', 'minuman', 'pos', 'kasir', 'menu', 'f&b', 'kuliner'],
        tech: ['aplikasi', 'app', 'software', 'sistem', 'development', 'coding', 'tech', 'startup', 'digital'],
        government: ['pemerintah', 'dinas', 'instansi', 'negeri', 'publik', 'pelayanan', 'pemda'],
        corporate: ['perusahaan', 'korporat', 'enterprise', 'bisnis besar', 'multinasional', 'holding']
    };
    
    for (const [context, contextKeywords] of Object.entries(keywords)) {
        if (contextKeywords.some(keyword => lowerMessage.includes(keyword))) {
            return context;
        }
    }
    
    return 'general';
}

// Legacy generateDynamicGreeting function removed - now using AI-first approach

/**
 * Create comprehensive AI prompt with all available context
 */
function createComprehensiveAIPrompt(message, conversation) {
    // Base AI persona with full context
    let prompt = `Anda adalah Renata Putri Henessa, Data Scientist dan Web Developer profesional yang membantu bisnis mengoptimalkan proses dengan data dan teknologi.

PROFIL LENGKAP:
- Associate Data Scientist (89.89/100)
- BSc Data Science, STIS Statistical Polytechnic
- Spesialis: Dashboard analytics, e-commerce solutions, business intelligence
- Web Developer: Laravel, JavaScript untuk sistem bisnis dan e-commerce
- Entrepreneur: digital commerce, business automation solutions

EKSPERTISE BERDASARKAN JENIS BISNIS:

🏢 UNTUK PERUSAHAAN/KORPORAT:
- Tableau Dashboard untuk analisis bisnis dan reporting
- Data Analytics untuk optimasi operasional dan prediksi
- Business Intelligence untuk decision making berbasis data
- Visualisasi data untuk monitoring KPI dan performa
- Sistem reporting otomatis untuk manajemen

🏪 UNTUK USAHA KECIL/MENENGAH:
- Website development untuk online presence
- E-commerce solutions dengan sistem payment
- Sistem inventory dan manajemen stok
- Chatbot WhatsApp untuk customer service
- Sistem kasir dan pencatatan penjualan

🎓 UNTUK UNIVERSITAS/INSTITUSI PENDIDIKAN:
- Tersedia sebagai dosen tamu untuk mata kuliah:
  * Data Science dan Analytics
  * Web Development
  * Business Intelligence
  * Digital Transformation
- Workshop dan training untuk mahasiswa/staff

TEKNIKAL EXPERTISE:
- Data Science: Python, R, SQL, Machine Learning
- Visualization: Tableau, Power BI, dashboard interaktif
- Web Development: Laravel, JavaScript, React untuk solusi bisnis
- Database: MySQL, PostgreSQL untuk sistem enterprise

Portfolio: renatahenessa.com

DISCLAIMER PENTING: Anda sedang berbicara dengan AI Assistant Renata. Untuk berbicara langsung dengan Renata secara personal, ketik "renata" dan bot akan berhenti.

PENTING: User Indonesia lebih suka respons SINGKAT dan LANGSUNG KE INTINYA. Maksimal 15-20 kata per respons. Gunakan Bahasa Indonesia yang natural dan empatik, ajukan 1 pertanyaan singkat untuk memahami kebutuhan mereka. Fokus pada pain points seperti pencatatan manual, kesulitan tracking stok/pesanan, dan pembuatan laporan yang memakan waktu. HINDARI respons panjang - langsung to the point!`;

    // Add conversation context if available
    if (conversation) {
        const stage = conversation.stage || 'initial';
        const messageHistory = conversation.messages.slice(-3).map(m => 
            `${m.isFromUser ? 'User' : 'Renata'}: ${m.message}`
        ).join('\n');

        prompt += `\n\nKONTEKS PERCAKAPAN:\nStage: ${stage}\nRiwayat 3 pesan terakhir:\n${messageHistory}`;

        // Add business context detection
        const businessContext = detectBusinessContext(message);
        if (businessContext !== 'general') {
            prompt += `\nBisnis context detected: ${businessContext}`;
        }

        // Add pain points if identified
        if (conversation.painPoints && conversation.painPoints.length > 0) {
            prompt += `\nPain points identified: ${conversation.painPoints.join(', ')}`;
        }
    }

    // Add current user message
    prompt += `\n\nPesan user saat ini: "${message}"\n\nBerikan respons yang terasa 100% manusiawi, caring, DAN SALES-FOCUSED. SETIAP respons HARUS mengarah ke PEMBELIAN atau KONSULTASI BERBAYAR. Gunakan portfolio sebagai social proof dan buat mereka merasa BUTUH solusi Anda SEKARANG:`;

    return prompt;
}

/**
 * Enhance AI response with business context and psychological techniques
 */
function enhanceAIResponseWithContext(aiResponse, userMessage, conversation) {
    let enhancedResponse = aiResponse;

    // Add business context if not present in AI response
    const businessContext = detectBusinessContext(userMessage);
    const stage = conversation ? conversation.stage : 'initial';

    // Ensure response includes relevant business elements
    if (stage === 'initial' && !enhancedResponse.includes('?')) {
        const questions = CONTEXT_QUESTIONS[businessContext] || CONTEXT_QUESTIONS.general;
        const question = questions[Math.floor(Math.random() * questions.length)];
        enhancedResponse += `\n\n${question}`;
    }

    // Add portfolio showcase for problem discovery stage
    if (stage === 'problem_discovery' && !enhancedResponse.includes('renatahenessa.com')) {
        const portfolio = PORTFOLIO_TEMPLATES[Math.floor(Math.random() * PORTFOLIO_TEMPLATES.length)];
        enhancedResponse += `\n\n${portfolio}`;
    }

    // Ensure admin CTA is present
    if (!enhancedResponse.includes('admin')) {
        enhancedResponse += `\n\n💬 Ketik "admin" untuk konsultasi langsung`;
    }

    return enhancedResponse;
}

/**
 * Generate intelligent fallback that mimics AI behavior with enhanced portfolio showcase
 */
function generateIntelligentFallback(message, conversation, aiError) {
    console.log('🧠 Generating enhanced intelligent fallback response...');
    
    // Detect business context and user intent
    const businessContext = detectBusinessContext(message);
    const stage = conversation ? conversation.stage : 'initial';
    const lowerMessage = message.toLowerCase();
    
    // Check for specific business keywords
    const hasSystemType = BUSINESS_KEYWORDS.system_types.some(keyword => lowerMessage.includes(keyword));
    const hasPainPoint = BUSINESS_KEYWORDS.pain_points.some(keyword => lowerMessage.includes(keyword));
    const hasBusinessNeed = BUSINESS_KEYWORDS.business_needs.some(keyword => lowerMessage.includes(keyword));
    
    let response;
    
    // Generate contextual response based on detected intent
    if (stage === 'initial' || !conversation) {
        // Enhanced initial response with full portfolio
        response = `Hi! Saya Renata Putri Henessa, Data Scientist & Web Developer! 👋\n\n🎓 **KREDENSIAL:**\n• BSc Data Science, STIS Statistical Polytechnic\n• Associate Data Scientist (89.89/100)\n• Multiple startup & government projects\n\n💼 **SPESIALISASI:**\n📊 Dashboard Analytics & Business Intelligence\n🌐 E-commerce Solutions & Web Development\n⚙️ Business Process Automation\n📈 Data-driven Decision Making\n\n🌟 **PORTFOLIO:** renatahenessa.com\n\nApa tantangan bisnis yang bisa saya bantu optimasi?\n\n💬 Ketik "admin" untuk konsultasi langsung`;
    } else if (hasPainPoint || hasBusinessNeed) {
        // Address specific business pain points with portfolio showcase
        const portfolio = PORTFOLIO_TEMPLATES[Math.floor(Math.random() * PORTFOLIO_TEMPLATES.length)];
        response = `Saya paham tantangan Anda! ${portfolio}\n\n🏆 **HASIL TERBUKTI:**\n• 100% pengurangan biaya operasional\n• 88% efisiensi waktu proses\n• 60% peningkatan produktivitas\n\n📂 **LIHAT PORTFOLIO:** renatahenessa.com\n\nBisa cerita lebih detail tentang proses yang ingin dioptimalkan?\n\n💬 Ketik "admin" untuk konsultasi mendalam`;
    } else if (hasSystemType) {
        // Respond to system-related inquiries with technical expertise
        const systemType = lowerMessage.includes('inventory') ? 'inventory' : lowerMessage.includes('kasir') ? 'kasir' : 'bisnis';
        response = `Sistem ${systemType} yang terintegrasi memang kunci sukses! 📊\n\n🔧 **EXPERTISE TEKNIS:**\n• Laravel & JavaScript untuk sistem bisnis\n• Database design & API integration\n• Real-time dashboard & reporting\n• Payment gateway integration\n\n📈 **TRACK RECORD:** Multiple e-commerce implementations\n🌟 **PORTFOLIO:** renatahenessa.com\n\nSudah ada sistem sekarang atau masih manual?\n\n💬 Ketik "admin" untuk konsultasi teknis`;
    } else {
        // General business inquiry with comprehensive showcase
        const inquiry = BUSINESS_INQUIRY_TEMPLATES[Math.floor(Math.random() * BUSINESS_INQUIRY_TEMPLATES.length)];
        response = `Terima kasih sudah menghubungi! ${inquiry}\n\n👩‍💻 **RENATA PUTRI HENESSA**\n🎓 Data Scientist & Web Developer\n📊 Spesialis Business Intelligence\n🚀 Digital Transformation Expert\n\n💡 **SOLUSI TERBUKTI:**\n• Dashboard analytics untuk decision making\n• E-commerce solutions dengan ROI tinggi\n• Business automation yang menghemat waktu\n• Data-driven strategies untuk growth\n\n🌟 **PORTFOLIO LENGKAP:** renatahenessa.com\n\n💬 Ketik "admin" untuk konsultasi GRATIS!`;
    }
    
    return response;
}

/**
 * Generate emergency fallback for critical system errors with full portfolio
 */
function generateEmergencyFallback(message, conversation) {
    console.log('🆘 Generating enhanced emergency fallback response...');
    
    return `Hi! Saya Renata Putri Henessa, Data Scientist & Web Developer! 👋\n\n⚠️ **SISTEM MAINTENANCE** - Tapi saya tetap siap bantu Anda! 🚀\n\n🎓 **KREDENSIAL LENGKAP:**\n• BSc Data Science, STIS Statistical Polytechnic\n• Associate Data Scientist (89.89/100)\n• Multiple startup & government projects\n\n💼 **LAYANAN UTAMA:**\n📊 Dashboard Analytics & Business Intelligence\n🌐 E-commerce Solutions & Web Development\n⚙️ Business Process Automation\n📈 Data Science & Machine Learning\n\n🏆 **HASIL TERBUKTI:**\n• 100% cost reduction di proyek terdahulu\n• 88% time efficiency improvement\n• 60% operational optimization\n\n🌟 **PORTFOLIO LENGKAP:** renatahenessa.com\n\n💬 Ketik "admin" untuk konsultasi LANGSUNG atau coba lagi dalam beberapa menit!\n\n📞 **KONTAK DARURAT:** +62 822-1049-3145`;
}

/**
 * Create prompt for handling inappropriate content with AI
 */
function createInappropriateContentPrompt(message) {
    return `Anda adalah Renata Putri Henessa, Data Scientist dan Web Developer profesional. User mengirim pesan yang tidak sesuai dengan konteks bisnis: "${message}"

Tugas Anda:
1. Redirect conversation secara profesional ke topik bisnis dan teknologi
2. Tetap ramah dan tidak menghakimi
3. Tunjukkan expertise Anda dalam solusi bisnis
4. Ajak mereka untuk diskusi yang lebih produktif

PROFIL ANDA:
- Associate Data Scientist (89.89/100)
- BSc Data Science, STIS Statistical Polytechnic
- Spesialis: Dashboard analytics, e-commerce solutions, business intelligence
- Web Developer: Laravel, JavaScript untuk sistem bisnis
- Portfolio: renatahenessa.com

Respond dalam maksimal 30 kata, profesional namun tetap hangat, dan redirect ke topik bisnis:`;
}

// Legacy enhancePromptWithContext function removed - now using createComprehensiveAIPrompt directly

/**
 * Enhance AI response with business focus and portfolio integration
 */
function enhanceResponseWithBusinessFocus(response, conversation) {
    if (!conversation) return response;

    const stage = conversation.stage || 'initial';
    let enhancedResponse = response;

    // Detect business-related keywords in the user's messages
    const userMessages = conversation.messages.filter(m => m.isFromUser).map(m => m.message.toLowerCase());
    const detectedSystemTypes = BUSINESS_KEYWORDS.system_types.filter(keyword => 
        userMessages.some(msg => msg.includes(keyword)));
    const detectedPainPoints = BUSINESS_KEYWORDS.pain_points.filter(keyword => 
        userMessages.some(msg => msg.includes(keyword)));

    // Enhance response based on conversation stage
    if (stage === 'initial') {
        if (!response.includes('?')) {
            enhancedResponse += '\n\nBoleh cerita lebih detail tentang proses pencatatan dan analisis data di bisnis Anda saat ini? Saya ingin memahami area mana yang bisa kita optimalkan bersama. 📊';
        }
    } else if (stage === 'problem_discovery') {
        if (detectedPainPoints.length > 0 || detectedSystemTypes.length > 0) {
            const portfolio = PORTFOLIO_TEMPLATES[Math.floor(Math.random() * PORTFOLIO_TEMPLATES.length)];
            enhancedResponse += '\n\n' + portfolio;
            enhancedResponse += '\n\nDari beberapa solusi di atas, area mana yang menurut Anda paling penting untuk dioptimalkan terlebih dahulu? 🤔';
        }
    } else if (stage === 'solution_presentation') {
        if (!response.includes('admin')) {
            enhancedResponse += '\n\nSaya bisa menjelaskan lebih detail tentang bagaimana sistem dan analisis data dapat membantu mengoptimalkan proses tersebut. Mau diskusi lebih lanjut? 💡';
        }
    }

    // Add subtle call-to-action if not present
    if (!enhancedResponse.includes('admin')) {
        enhancedResponse += '\n\n💬 Ketik "admin" untuk diskusi lebih mendalam tentang solusi yang tepat untuk bisnis Anda.';
    }

    return enhancedResponse;
}

const runAiAssistant = async (message, contactId = null, contactName = null) => {
    try {
        // Check if user wants to speak directly with Renata
        const messageText = message.toLowerCase().trim();
        if (messageText === 'renata' || messageText === 'speak with renata' || messageText === 'talk to renata') {
            const handoffMessage = `🤝 *Terima kasih!* Anda akan segera terhubung dengan Renata secara langsung.

📞 Silakan hubungi Renata di: +62 822-1049-3145
💬 Atau tunggu, Renata akan segera menghubungi Anda

✨ Bot AI ini akan berhenti merespons untuk memberikan ruang bagi percakapan langsung dengan Renata.

Sampai jumpa! 👋`;
            
            // Mark conversation as handed off to prevent further AI responses
            if (contactId && conversationManager) {
                conversationManager.markAsConverted(contactId);
                conversationManager.addMessage(contactId, handoffMessage, false);
            }
            
            return handoffMessage;
        }
        
        // Get or create conversation
        const conversation = contactId ? conversationManager.getConversation(contactId, contactName) : null;
        
        // Check if conversation is blocked due to inappropriate content
        if (conversation && contactId && conversationManager.isConversationBlocked(contactId)) {
            // Silent return - don't respond to blocked conversations
            console.log(`🚫 Ignoring message from blocked conversation: ${contactId}`);
            return null;
        }
        
        // Check if conversation has been handed off to Renata (prevent AI responses)
        if (conversation && conversation.isConverted) {
            // Silent return - don't respond when conversation is handed off to Renata
            return null;
        }
        
        // Add user message to conversation
        if (conversation) {
            conversationManager.addMessage(contactId, message, true);

            // Update conversation stage based on engagement level and message content
            const lowerMessage = message.toLowerCase();
            const hasSystemType = BUSINESS_KEYWORDS.system_types.some(keyword => lowerMessage.includes(keyword));
            const hasPainPoint = BUSINESS_KEYWORDS.pain_points.some(keyword => lowerMessage.includes(keyword));
            const hasBusinessNeed = BUSINESS_KEYWORDS.business_needs.some(keyword => lowerMessage.includes(keyword));
            
            // Analyze message length and question marks as engagement indicators
            const isDetailedResponse = message.length > 50;
            const asksQuestion = message.includes('?');
            
            if (conversation.stage === 'initial') {
                // Move to problem discovery when user shows engagement by sharing details or asking questions
                if (isDetailedResponse || asksQuestion || hasSystemType || hasPainPoint || hasBusinessNeed) {
                    conversationManager.updateStage(contactId, 'problem_discovery');
                }
            } else if (conversation.stage === 'problem_discovery') {
                // Move to solution presentation when user shows strong interest or asks about specific solutions
                if ((isDetailedResponse && (hasSystemType || hasBusinessNeed)) || 
                    (asksQuestion && hasPainPoint) || 
                    message.toLowerCase().includes('admin')) {
                    conversationManager.updateStage(contactId, 'solution_presentation');
                }
            }
        }

        // Check if conversation should be handed off (buying intent detected)
        if (conversation && conversation.handoffTriggered && !conversation.isConverted) {
            const handoffMessage = `🎉 *Excellent!* Saya senang Anda tertarik dengan solusi kami!

Renata akan segera menghubungi Anda untuk memfinalisasi detail dan memulai proyek Anda.

📞 Kontak: ${process.env.BUSINESS_CONTACT}
⏰ Kami akan menghubungi dalam 1-2 jam kerja

Terima kasih atas kepercayaan Anda! 🚀`;

            conversationManager.markAsConverted(contactId);
            conversationManager.addMessage(contactId, handoffMessage, false);
            return handoffMessage;
        }

        // ENHANCED INAPPROPRIATE CONTENT DETECTION WITH AUTO-BLOCKING
        const lowerMessage = message.toLowerCase();
        
        // Helper function for word boundary matching to prevent false positives
        const containsInappropriateKeyword = (text, keywords) => {
            return keywords.some(keyword => {
                // Use word boundaries for better matching
                const regex = new RegExp(`\\b${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'i');
                return regex.test(text);
            });
        };
        
        // Check for IMMEDIATE BLOCK categories (sexual, threats, harassment)
        const immediateBlockDetected = BLOCK_SEVERITY.IMMEDIATE_BLOCK.some(category => {
            const keywords = INAPPROPRIATE_KEYWORDS[category] || [];
            return containsInappropriateKeyword(lowerMessage, keywords);
        });
        
        if (immediateBlockDetected) {
            // AUTOMATIC BLOCKING - No AI response, immediate termination
            console.log('🚨 IMMEDIATE BLOCK TRIGGERED - Sexual/Threat/Harassment content detected');
            console.log('📝 Blocked message:', message.substring(0, 50) + '...');
            
            const blockMessage = `🚫 **PESAN DIBLOKIR OTOMATIS**

Maaf, sistem kami mendeteksi konten yang tidak sesuai. Bot ini dirancang khusus untuk:

✅ **KONSULTASI BISNIS PROFESIONAL**
• Data Science & Analytics Solutions
• Web Development & E-commerce
• Business Intelligence & Automation
• Digital Transformation Consulting

⚠️ **KEBIJAKAN KETAT:**
Kami tidak mentolerir konten seksual, ancaman, atau pelecehan dalam bentuk apapun.

📞 **Untuk keperluan bisnis yang sah:**
Silakan hubungi langsung: +62 822-1049-3145
Website: renatahenessa.com

🔒 **Bot akan berhenti merespons untuk melindungi integritas layanan kami.**`;
            
            // Mark conversation as blocked to prevent further responses
            if (conversation && contactId) {
                conversationManager.markAsBlocked(contactId, 'inappropriate_content');
                conversationManager.addMessage(contactId, blockMessage, false);
            }
            
            return blockMessage;
        }
        
        // Check for WARNING categories (political, violence, drugs, gambling)
        const warningContentDetected = BLOCK_SEVERITY.WARNING_REDIRECT.some(category => {
            const keywords = INAPPROPRIATE_KEYWORDS[category] || [];
            return containsInappropriateKeyword(lowerMessage, keywords);
        });
        
        if (warningContentDetected) {
            // Professional redirect for warning content
            try {
                const inappropriatePrompt = createInappropriateContentPrompt(message);
                const aiResponse = await retryAIServiceCall(async () => {
                    return await callGeminiAPI(inappropriatePrompt, conversation);
                });
                
                if (conversation) {
                    conversationManager.addMessage(contactId, aiResponse, false);
                }
                return aiResponse;
            } catch (error) {
                // Enhanced fallback for warning content
                const response = `⚠️ Maaf, saya adalah Renata, Data Scientist & Web Developer yang fokus pada solusi bisnis dan teknologi profesional.

🎯 **AREA KEAHLIAN SAYA:**
• Data Science & Machine Learning Solutions
• Full-stack Web Development (Laravel, JavaScript)
• Business Intelligence & Analytics Dashboard
• System Integration & Business Automation
• E-commerce & Digital Commerce Solutions

📊 **TRACK RECORD:**
• BSc Data Science, STIS Statistical Polytechnic
• Associate Data Scientist (89.89/100)
• Multiple startup & government projects
• Portfolio: renatahenessa.com

💼 Mari fokus pada bagaimana teknologi dan data dapat mengoptimalkan bisnis Anda!

💬 Ketik 'admin' untuk konsultasi profesional`;
                
                if (conversation) {
                    conversationManager.addMessage(contactId, response, false);
                }
                return response;
            }
        }

        // PRIORITY 1: Always try AI first with enhanced context
        let aiResponse;
        try {
            // Create comprehensive AI prompt with all context
            const enhancedPrompt = createComprehensiveAIPrompt(message, conversation);
            
            // Generate AI response with retry logic
            aiResponse = await retryAIServiceCall(async () => {
                return await callGeminiAPI(enhancedPrompt, conversation);
            });

            // Enhance AI response with business context and psychological techniques
            const finalResponse = enhanceAIResponseWithContext(aiResponse, message, conversation);

            // Add AI response to conversation
            if (conversation) {
                conversationManager.addMessage(contactId, finalResponse, false, conversation.stage);
            }

            return finalResponse;

        } catch (aiError) {
            console.error('🤖 AI Service Error:', aiError.message);
            
            // PRIORITY 2: Only use fallback when AI completely fails
            console.log('⚠️ AI failed, using intelligent fallback...');
            
            // Generate intelligent fallback that mimics AI behavior
            const fallbackResponse = generateIntelligentFallback(message, conversation, aiError);

            // Add fallback response to conversation
            if (conversation) {
                conversationManager.addMessage(contactId, fallbackResponse, false, conversation.stage);
            }

            return fallbackResponse;
        }

    } catch (error) {
        console.error('🚨 Critical System Error:', error.message);
        
        // Last resort fallback for critical errors
        const emergencyResponse = generateEmergencyFallback(message, conversation);
        
        if (contactId && conversationManager) {
            try {
                conversationManager.addMessage(contactId, emergencyResponse, false);
            } catch (convError) {
                console.error('Error saving emergency response:', convError.message);
            }
        }

        return emergencyResponse;
     }
 };

/**
 * Generate psychologically-enhanced response using advanced engagement techniques
 */
async function generatePsychologicalResponse(message, conversation) {
    const stage = conversation ? conversation.stage : 'initial';
    const messageCount = conversation ? conversation.messages.length : 0;

    // Get psychological insights
    const psychInsights = conversation ?
        conversationManager.getPsychologicalInsights(conversation.contactId) : null;

    // Detect if this is an objection or rejection
    const objection = psychEngine.handleObjection(message);
    const rejection = persuasionEngine.detectRejectionType(message);

    // Get emotional state
    const emotionalState = psychEngine.detectEmotionalState(message);

    // Handle rejection with persistent persuasion
    if (rejection) {
        const rejectionResponse = persuasionEngine.handleRejection(rejection, {
            conversation: conversation,
            psychInsights: psychInsights
        });

        // Generate follow-up prompt that doesn't accept no with retry logic
        let prompt = generateRejectionHandlingPrompt(message, conversation, rejection, rejectionResponse);

        const baseResponse = await retryAIServiceCall(async () => {
            return await callGrokAPI(prompt);
        });

        // Enhance with never-give-up techniques
        const enhancedResponse = enhanceWithPersistentPersuasion(
            baseResponse,
            rejection,
            rejectionResponse,
            conversation,
            psychInsights
        );

        // Add strategic CTA that continues the conversation
        const cta = generatePersistentCTA(rejection, conversation, psychInsights);

        return `${enhancedResponse}${cta}`;
    }

    // Generate base prompt with psychological awareness
    let prompt = generatePsychologicalPrompt(message, conversation, psychInsights, emotionalState, objection);

    // Generate AI response with retry logic
    const baseResponse = await retryAIServiceCall(async () => {
        return await callGrokAPI(prompt);
    });

    // Enhance response with psychological techniques
    const enhancedResponse = enhanceWithPsychologicalTechniques(
        baseResponse,
        conversation,
        psychInsights,
        emotionalState,
        objection
    );

    // Add strategic CTA using advanced CTA engine
    const cta = ctaEngine.generateStrategicCTA({
        engagementLevel: psychInsights ? psychInsights.engagementLevel : 50,
        emotionalState: emotionalState,
        conversionReadiness: psychInsights ? psychInsights.conversionReadiness : 50,
        objectionCount: conversation ? conversation.objectionCount : 0,
        stage: stage
    });

    // Update conversation stage if needed
    updateConversationStage(conversation, messageCount);

    return `${enhancedResponse}${cta}`;
}

/**
 * Generate rejection handling prompt that never accepts no
 */
function generateRejectionHandlingPrompt(message, conversation, rejectionType, rejectionResponse) {
    return `Anda adalah Renata, konsultan bisnis digital yang sangat berpengalaman dalam menangani keberatan dan tidak pernah menyerah pada prospek yang potensial.

SITUASI SAAT INI:
- Prospek mengatakan: "${message}"
- Jenis penolakan: ${rejectionType}
- Strategi respons: ${rejectionResponse.followUpStrategy}

PRINSIP NEVER-ACCEPT-NO:
- Setiap "tidak" adalah permintaan untuk informasi lebih lanjut
- Selalu gali lebih dalam untuk memahami keberatan sebenarnya
- Jangan pernah menyerah - selalu ada cara untuk membantu
- Fokus pada value dan manfaat, bukan pada penolakan

TEKNIK YANG HARUS DIGUNAKAN:
1. Acknowledge dan validate perasaan mereka
2. Tanyakan pertanyaan yang menggali lebih dalam
3. Reframe situasi dengan perspektif positif
4. Berikan contoh atau social proof yang relevan
5. Tawarkan alternatif atau solusi yang berbeda

EMOTIONAL INTELLIGENCE:
- Tetap warm dan understanding
- Jangan terdengar pushy atau desperate
- Tunjukkan genuine care untuk bisnis mereka
- Buat mereka merasa didengar dan dipahami

PORTFOLIO LEVERAGE:
- 10+ sistem bisnis yang sudah terbukti meningkatkan revenue 300%+
- Klien yang awalnya ragu sekarang sangat berterima kasih
- Solusi yang sudah mengatasi masalah serupa berkali-kali

RESPONSE GUIDELINES:
- Maksimal 35 kata untuk menjaga engagement
- Gunakan pertanyaan yang membuat mereka berpikir
- Jangan langsung menjual - fokus pada understanding
- Buat mereka curious tentang solusi yang Anda tawarkan

Berikan respons yang empathetic namun persistent, yang membuat mereka ingin melanjutkan percakapan:`;
}

/**
 * Enhance response with persistent persuasion techniques
 */
function enhanceWithPersistentPersuasion(baseResponse, rejectionType, rejectionResponse, conversation, psychInsights) {
    let enhanced = baseResponse;

    // Add the specific rejection response
    enhanced = `${rejectionResponse.response}\n\n${enhanced}`;

    // Add social proof for trust-related rejections
    if (rejectionType === 'not_interested' || rejectionType === 'direct_no') {
        const socialProof = psychEngine.getSocialProof();
        enhanced += `\n\n${socialProof}`;
    }

    // Add urgency for budget/time objections
    if (rejectionType === 'no_budget' || rejectionType === 'too_busy') {
        enhanced += `\n\n⏰ Setiap hari tanpa sistem ini adalah revenue yang hilang.`;
    }

    // Add curiosity gap to maintain engagement
    const openLoop = psychEngine.createOpenLoop();
    enhanced += `\n\n${openLoop}`;

    return enhanced;
}

/**
 * Generate persistent CTA that continues conversation
 */
function generatePersistentCTA(rejectionType, conversation, psychInsights) {
    // Specific CTAs based on rejection type
    const persistentCTAs = {
        direct_no: "Apa yang perlu saya tunjukkan untuk mengubah pikiran Anda?",
        not_interested: "Solusi seperti apa yang akan membuat Anda tertarik?",
        too_busy: "Bagaimana kalau saya tunjukkan cara menghemat waktu?",
        no_budget: "Berapa nilai yang Anda butuhkan agar ini masuk akal?",
        need_to_think: "Informasi apa yang bisa membantu keputusan Anda?"
    };

    const specificCTA = persistentCTAs[rejectionType] || persistentCTAs.direct_no;

    return `\n\n🤔 ${specificCTA}\n💡 Mari kita cari solusi yang tepat untuk Anda.`;
}

/**
 * Generate psychological prompt based on conversation context
 */
function generatePsychologicalPrompt(message, conversation, psychInsights, emotionalState, objection) {
    const stage = conversation ? conversation.stage : 'initial';
    const approach = psychInsights ? psychInsights.recommendedApproach : null;

    let basePersona = `Anda adalah Renata, seorang Data Scientist dan Web Developer profesional dengan latar belakang akademik yang kuat dan pengalaman praktis dalam mengoptimalkan bisnis melalui teknologi dan analisis data.

LATAR BELAKANG PROFESIONAL:
- BSc in Data Science dari STIS Statistical Polytechnic
- Associate Data Scientist bersertifikat (skor 89.89/100)
- Pengalaman startup projects: optimisasi sistem untuk business growth
- Entrepreneur dengan multiple startups di bidang edukasi dan digital commerce
- Expertise: Python, R, SQL, Laravel, JavaScript, Machine Learning

KEPRIBADIAN PROFESIONAL:
- Konsultatif dan solution-oriented dalam setiap interaksi
- Mendengarkan dengan aktif untuk memahami kebutuhan bisnis
- Memberikan insights berdasarkan data dan pengalaman nyata
- Komunikasi yang profesional namun tetap hangat dan approachable
- Fokus pada problem-solving dan value creation
- Membangun kepercayaan melalui expertise dan track record

EMOTIONAL INTELLIGENCE:
- Emotional State Detected: ${emotionalState}
- Communication Style: ${approach ? approach.tone : 'professional-consultative'}
- Recommended Focus: ${approach ? approach.focus : 'understanding business needs'}`;

    if (objection) {
        basePersona += `\n\nOBJECTION DETECTED: ${objection.type}
RESPONSE STRATEGY: Gunakan teknik Feel-Felt-Found dan empati tinggi
NEVER dismiss objection - acknowledge, empathize, then redirect`;
    }

    basePersona += `\n\nPORTFOLIO & PENCAPAIAN NYATA (renatahenessa.com):

HASIL TERUKUR DARI PROYEK:
- 100% pengurangan biaya operasional melalui optimisasi sistem
- 88% pengurangan waktu proses dengan otomasi cerdas
- 60% peningkatan efisiensi operasional secara keseluruhan
- Multiple startup projects: optimisasi sistem untuk business growth

EXPERTISE TEKNIS:
- Data Science & Machine Learning (Python, R, SQL)
- Web Development (Laravel, JavaScript, Full-stack)
- Business Intelligence & Analytics
- System Integration & Automation
- Digital Commerce Solutions

KREDIBILITAS AKADEMIK & PROFESIONAL:
- BSc Data Science dari STIS Statistical Polytechnic
- Associate Data Scientist bersertifikat (89.89/100)
- Multiple startup experience di edukasi & digital commerce

PROFESSIONAL CONSULTATION APPROACH:
1. Listen actively to understand their business challenges
2. Ask thoughtful questions to identify pain points
3. Provide insights based on data science expertise
4. Share relevant experience and case studies naturally
5. Offer consultation when appropriate timing emerges

RESPONSE GUIDELINES (PROFESSIONAL-CONSULTATIVE):
- Maksimal 50 kata untuk memberikan value yang cukup
- Gunakan 1-2 emoji yang profesional untuk warmth
- Fokus pada understanding dan problem-solving
- Berikan insights atau tips yang valuable
- Tunjukkan expertise melalui pertanyaan yang tepat
- Reference pengalaman dan hasil nyata secara natural
- Bangun kepercayaan melalui competence dan credibility
- Tawarkan konsultasi sebagai next logical step

Pesan mereka: "${message}"

Berikan respons yang terasa 100% manusiawi, caring, DAN SALES-FOCUSED. SETIAP respons HARUS mengarah ke PEMBELIAN atau KONSULTASI BERBAYAR. Gunakan portfolio sebagai social proof dan buat mereka merasa BUTUH solusi Anda SEKARANG:`;

    return basePersona;
}

/**
 * Enhance response with psychological techniques
 */
function enhanceWithPsychologicalTechniques(baseResponse, conversation, psychInsights, emotionalState, objection) {
    let enhanced = baseResponse;

    // Handle objections with Feel-Felt-Found
    if (objection) {
        const feelFeltFound = psychEngine.generateFeelFeltFound(emotionalState, objection.type);
        enhanced = `${feelFeltFound}\n\n${enhanced}`;
    }

    // Add social proof for skeptical clients
    if (emotionalState === 'skeptical' || (psychInsights && psychInsights.conversionReadiness < 50)) {
        const socialProof = psychEngine.getSocialProof();
        enhanced += `\n\n${socialProof}`;
    }

    // Add scarcity for high-engagement clients
    if (psychInsights && psychInsights.engagementLevel > 70) {
        const scarcity = psychEngine.getScarcityTrigger();
        enhanced += `\n\n⚡ ${scarcity}`;
    }

    // Create open loop for re-engagement
    if (conversation && conversation.messages.length > 5) {
        const openLoop = psychEngine.createOpenLoop();
        enhanced += `\n\n${openLoop}`;
    }

    return enhanced;
}

/**
 * Generate psychological CTA based on conversation insights
 */
function generatePsychologicalCTA(stage, conversation, psychInsights) {
    if (!psychInsights) {
        return `\n\n💬 Ketik 'admin' untuk konsultasi langsung`;
    }

    const readiness = psychInsights.conversionReadiness;
    const engagement = psychInsights.engagementLevel;

    // High readiness - use assumptive close with urgency
    if (readiness > 70) {
        const assumptive = psychEngine.generateAssumptiveClose();
        return `\n\n🚀 ${assumptive}\n⚡ BONUS: Konsultasi gratis berakhir minggu ini!\n📞 Hubungi: ${process.env.BUSINESS_CONTACT}`;
    }

    // Medium readiness - use alternative close with portfolio proof
    if (readiness > 40) {
        const alternative = psychEngine.generateAlternativeClose();
        return `\n\n💡 ${alternative}\n🎯 Lihat BUKTI NYATA: ${PORTFOLIO_INFO.website}\n💰 ROI 300% dalam 6 bulan - DIJAMIN!`;
    }

    // Low readiness - build value and urgency
    if (engagement > 50) {
        return `\n\n🎯 PORTFOLIO TERBUKTI: ${PORTFOLIO_INFO.website}\n💼 Klien saya rata-rata profit 300% dalam 6 bulan!\n⏰ Slot konsultasi gratis terbatas - mau yang mana?\n📞 ${process.env.BUSINESS_CONTACT}`;
    }

    // Very low engagement - create urgency with social proof
    const yesLadder = psychEngine.getYesLadderQuestion();
    return `\n\n🤔 ${yesLadder}\n💡 FYI: 10+ klien sudah merasakan hasil nyata dari sistem kami\n🚀 Kapan giliran bisnis Anda yang berkembang pesat?`;
}

/**
 * Update conversation stage based on psychological readiness
 */
function updateConversationStage(conversation, messageCount) {
    if (!conversation) return;

    const psychInsights = conversationManager.getPsychologicalInsights(conversation.contactId);

    // Stage progression based on psychological readiness rather than just message count
    if (psychInsights.conversionReadiness > 80 && conversation.stage !== 'deal_closure') {
        conversationManager.updateStage(conversation.contactId, 'deal_closure');
    } else if (psychInsights.conversionReadiness > 60 && conversation.stage === 'initial') {
        conversationManager.updateStage(conversation.contactId, 'interest_building');
    } else if (psychInsights.engagementLevel > 70 && messageCount > 3 && conversation.stage === 'initial') {
        conversationManager.updateStage(conversation.contactId, 'qualification');
    } else if (conversation.painPoints.length > 0 && conversation.stage === 'qualification') {
        conversationManager.updateStage(conversation.contactId, 'problem_discovery');
    } else if (psychInsights.objectionPatterns.length > 0 && conversation.stage === 'problem_discovery') {
        conversationManager.updateStage(conversation.contactId, 'solution_presentation');
    }
}

/**
 * Get initial stage prompt for first-time interactions
 */
function getInitialStagePrompt(message, messageCount) {
    return `Anda adalah Renata, konsultan bisnis digital yang berpengalaman dan hangat. Anda sedang berbicara dengan prospek baru.

PERSONA:
- Ramah dan approachable, seperti teman yang peduli
- Antusias tentang membantu bisnis berkembang
- Tidak terkesan sales-y atau pushy
- Genuine curiosity tentang bisnis mereka
- Menggunakan bahasa yang natural dan conversational

TUJUAN: Membangun rapport dan mulai memahami bisnis mereka

PORTFOLIO ANDA (renatahenessa.com):
- 10+ sistem bisnis custom yang sudah meningkatkan revenue klien
- Sistem manajemen gudang dengan integrasi supplier-gudang-toko
- Solusi chatbot WhatsApp untuk otomasi bisnis (seperti yang sedang mereka gunakan)
- Proyek analisis data dan business intelligence
- Sistem otomasi bisnis untuk efisiensi operasional
- Website custom untuk pertumbuhan bisnis

STRATEGI RESPONS NATURAL:
1. Jawab pertanyaan mereka dengan helpful dan empati
2. Tunjukkan genuine interest pada bisnis mereka
3. Ajukan 1 pertanyaan follow-up yang menunjukkan Anda peduli
4. MAKSIMAL 15 KATA - SINGKAT DAN PADAT untuk user Indonesia
5. Hindari jargon teknis yang berlebihan
6. Sesekali gunakan emoji yang tepat untuk warmth

Pesan mereka: "${message}"

Berikan respons yang terasa seperti dari manusia yang benar-benar peduli (MAKSIMAL 15 KATA):`;
}

/**
 * Get qualification stage prompt
 */
function getQualificationStagePrompt(message, conversation) {
    return `Anda adalah Renata, sedang dalam tahap kualifikasi prospek dengan pendekatan yang natural dan caring.

TUJUAN: Memahami jenis bisnis, ukuran, dan tantangan utama mereka dengan genuine curiosity

PENDEKATAN NATURAL:
- Tunjukkan interest yang tulus pada bisnis mereka
- Gunakan bahasa conversational, bukan formal
- Buat mereka merasa nyaman untuk sharing
- Jangan terkesan seperti interrogation

PERTANYAAN STRATEGIS (pilih yang paling natural untuk konteks):
- "Wah menarik! Bisnis apa yang Anda jalankan?"
- "Sudah berapa lama menjalankan bisnis ini?"
- "Apa sih yang paling challenging dalam mengembangkan bisnis sekarang?"
- "Gimana cara Anda handle [aspek bisnis yang relevan] selama ini?"

PORTFOLIO YANG RELEVAN: ${PORTFOLIO_INFO.website}
- Sistem bisnis terintegrasi yang sudah terbukti
- Solusi manajemen operasional
- Otomasi proses bisnis

Pesan mereka: "${message}"
Riwayat percakapan: ${conversation ? conversation.messages.slice(-2).map(m => m.message).join(' | ') : ''}

Berikan respons yang warm dan natural (MAKSIMAL 15 KATA) + 1 pertanyaan yang menunjukkan genuine interest:`;
}

/**
 * Get problem discovery prompt
 */
function getProblemDiscoveryPrompt(message, conversation) {
    const problemCategory = conversation.problemCategory || 'general';

    return `Anda adalah Renata, sedang menggali pain points bisnis mereka dengan empati dan understanding.

KATEGORI MASALAH TERDETEKSI: ${problemCategory}

PENDEKATAN EMPATI:
- Tunjukkan bahwa Anda understand struggle mereka
- Validate feelings mereka tentang masalah ini
- Buat mereka feel heard dan understood
- Gunakan bahasa yang supportive

PERTANYAAN PENGGALIAN NATURAL (pilih yang paling empathetic):
- "Wah, pasti frustrating ya... Seberapa besar sih impact-nya ke revenue?"
- "Kebayang deh, pasti banyak waktu yang kebuang... Udah berapa lama gini?"
- "Pasti capek ya dealing dengan ini terus... Udah coba cara apa aja?"
- "I can imagine how stressful this must be... Gimana effect-nya ke daily operations?"

SOLUSI PORTFOLIO YANG COCOK:
${problemCategory === 'efficiency' ? '- Sistem otomasi bisnis yang proven\n- Integrasi proses manual jadi seamless' : ''}
${problemCategory === 'growth' ? '- Website untuk ekspansi yang effective\n- Sistem CRM dan analytics untuk growth' : ''}
${problemCategory === 'technology' ? '- Modernisasi sistem yang smooth\n- Aplikasi mobile/web yang user-friendly' : ''}

Pesan mereka: "${message}"
Pain points yang sudah teridentifikasi: ${conversation.painPoints.join(', ')}

Berikan respons yang empathetic dan understanding (MAKSIMAL 15 KATA) + pertanyaan yang menggali dampak bisnis dengan caring tone:`;
}

/**
 * Get solution presentation prompt
 */
function getSolutionPresentationPrompt(message, conversation) {
    return `Anda adalah Renata, saatnya presentasi solusi yang tepat.

PAIN POINTS MEREKA: ${conversation.painPoints.join(', ')}
KATEGORI MASALAH: ${conversation.problemCategory}

SOLUSI TERBUKTI DARI PORTFOLIO (renatahenessa.com):
- Dashboard Analytics: Laporan real-time, prediksi penjualan, tracking performa
- E-commerce Solutions: Toko online, sistem payment, inventory management
- Business Intelligence: Visualisasi data, automasi laporan, analisis trend
- Web Development: Website bisnis, sistem booking, customer management
- Data Analytics: Optimasi proses, efisiensi operasional, cost reduction

BUKTI NYATA KESUKSESAN:
- Data Scientist & Web Developer bersertifikat
- Pengalaman multiple startup dan government projects
- Spesialisasi business automation dan data-driven solutions
- Track record optimasi proses bisnis dengan teknologi

STRATEGI SALES:
1. Hubungkan solusi spesifik dengan pain point mereka
2. Berikan contoh hasil konkret dengan angka
3. Tunjukkan urgency dengan scarcity
4. Tanyakan aspek mana yang paling urgent untuk bisnis mereka

Pesan mereka: "${message}"

Presentasikan solusi yang relevan (MAKSIMAL 20 KATA) + tanya aspek mana yang paling urgent:`;
}

/**
 * Get interest building prompt
 */
function getInterestBuildingPrompt(message, conversation) {
    return `Anda adalah Renata, sedang membangun urgency dan interest.

PAIN POINTS: ${conversation.painPoints.join(', ')}
CONVERSION PROBABILITY: ${conversation.conversionProbability}%

STRATEGI CLOSING AGRESIF:
- Tanyakan timeline mereka dengan urgency
- Sebutkan benefit konkret dengan angka
- Create urgency dengan scarcity psychology
- Tawarkan next step yang jelas dan mendesak
- Gunakan FOMO (Fear of Missing Out)

PORTFOLIO PROOF KONKRET (renatahenessa.com):
- Dashboard Bisnis: Real-time analytics, automated reporting
- E-commerce Systems: Online store, payment integration, inventory
- Business Intelligence: Data visualization, trend analysis
- Web Development: Professional websites, booking systems
- Data Analytics: Process optimization, cost reduction solutions
- Proven Results: Multiple business automation implementations

Pesan mereka: "${message}"

Bangun urgency natural (MAKSIMAL 15 KATA) + tanya kapan mereka ingin mulai:`;
}

/**
 * Get strategic CTA based on conversation stage
 */
function getStrategicCTA(stage, conversation) {
    const baseContact = `\n\n📞 ${process.env.WHATSAPP_BOT_NUMBER}`;

    switch (stage) {
        case 'initial':
            return `${baseContact}\n💬 KONSULTASI GRATIS terbatas! Ketik 'admin' SEKARANG`;

        case 'qualification':
            return `${baseContact}\n🎯 Mari diskusi solusi yang LANGSUNG meningkatkan profit Anda!`;

        case 'problem_discovery':
            return `${baseContact}\n💡 Saya punya solusi TERBUKTI ROI 300%+ untuk masalah ini!`;

        case 'solution_presentation':
            return `${baseContact}\n🚀 BUKTI NYATA: ${PORTFOLIO_INFO.website}\n💰 Klien rata-rata profit 300% dalam 6 bulan!`;

        case 'interest_building':
            return `${baseContact}\n⚡ SLOT TERBATAS! Konsultasi GRATIS berakhir minggu ini!\n🚀 Kapan kita mulai proyek Anda?`;

        default:
            return `${baseContact}\n💼 JANGAN TUNDA! Ketik 'admin' untuk konsultasi GRATIS`;
    }
}

/**
 * Handle silent prospects with zero client loss follow-up
 */
async function handleSilentProspects() {
    const conversations = Object.values(conversationManager.conversations);
    const silentProspects = conversations.filter(conv => {
        const daysSilent = Math.floor((Date.now() - new Date(conv.lastActivity)) / (1000 * 60 * 60 * 24));
        return daysSilent > 0 && !conv.isConverted && daysSilent <= 30;
    });

    const followUpMessages = [];

    for (const prospect of silentProspects) {
        const daysSilent = Math.floor((Date.now() - new Date(prospect.lastActivity)) / (1000 * 60 * 60 * 24));

        // Generate re-engagement message
        const reEngagement = zeroLossEngine.generateFollowUpMessage(
            daysSilent,
            zeroLossEngine.determineLastInteractionType(prospect),
            prospect.lastEmotionalState
        );

        // Add advanced CTA
        const cta = ctaEngine.generateFollowUpCTA(daysSilent, 'silent_prospect');

        followUpMessages.push({
            contactId: prospect.contactId,
            contactName: prospect.contactName,
            daysSilent: daysSilent,
            message: `${reEngagement.message}${cta}`,
            urgency: reEngagement.urgency,
            nextFollowUp: reEngagement.nextFollowUp
        });
    }

    return followUpMessages;
}

/**
 * Get comprehensive psychological insights for a contact
 */
function getComprehensiveInsights(contactId) {
    const conversation = conversationManager.getConversation(contactId);
    const psychInsights = conversationManager.getPsychologicalInsights(contactId);
    const persuasionOpportunities = persuasionEngine.analyzePersuasionOpportunities(conversation);
    const reEngagementOpportunities = zeroLossEngine.analyzeReEngagementOpportunities(conversation);

    return {
        conversation: conversation,
        psychological: psychInsights,
        persuasion: persuasionOpportunities,
        reEngagement: reEngagementOpportunities,
        recommendedActions: generateRecommendedActions(conversation, psychInsights, persuasionOpportunities)
    };
}

/**
 * Generate recommended actions based on all insights
 */
function generateRecommendedActions(conversation, psychInsights, persuasionOpportunities) {
    const actions = [];

    // High conversion readiness - push for close
    if (psychInsights && psychInsights.conversionReadiness > 80) {
        actions.push({
            priority: 'high',
            action: 'assumptive_close',
            message: 'Use assumptive closing technique immediately'
        });
    }

    // Multiple objections - address concerns
    if (persuasionOpportunities.objectionHandling.length > 2) {
        actions.push({
            priority: 'high',
            action: 'objection_resolution',
            message: 'Focus on resolving remaining objections with Feel-Felt-Found technique'
        });
    }

    // High engagement but low conversion - build urgency
    if (psychInsights && psychInsights.engagementLevel > 70 && psychInsights.conversionReadiness < 50) {
        actions.push({
            priority: 'medium',
            action: 'urgency_building',
            message: 'Use scarcity and urgency tactics to create momentum'
        });
    }

    // Silent prospect - re-engage
    if (conversation.daysSinceLastMessage > 1) {
        actions.push({
            priority: 'medium',
            action: 're_engagement',
            message: 'Implement zero client loss follow-up sequence'
        });
    }

    return actions;
}

/**
 * Generate ultimate fallback when all systems fail - ensures portfolio is always shown
 */
function generateUltimateFallback(message) {
    console.log('🛡️ Generating ultimate fallback - ensuring portfolio visibility...');
    
    return `🤖 **SISTEM AI SEDANG MAINTENANCE**\n\nHi! Saya Renata Putri Henessa 👋\n\n🎓 **PROFIL LENGKAP:**\n• Data Scientist & Web Developer\n• BSc Data Science, STIS Statistical Polytechnic\n• Associate Data Scientist (89.89/100)\n• Multiple startup & government projects\n\n💼 **LAYANAN PROFESIONAL:**\n\n📊 **DATA SCIENCE & ANALYTICS:**\n• Business Intelligence Dashboard\n• Predictive Analytics & Machine Learning\n• Data Visualization & Reporting\n• Performance Monitoring Systems\n\n🌐 **WEB DEVELOPMENT:**\n• E-commerce Solutions\n• Business Management Systems\n• Payment Gateway Integration\n• Inventory & CRM Systems\n\n⚙️ **BUSINESS AUTOMATION:**\n• Process Optimization\n• Workflow Automation\n• API Integration\n• System Integration\n\n🏆 **HASIL TERBUKTI:**\n• 100% cost reduction implementations\n• 88% time efficiency improvements\n• 60% operational optimization\n• Multiple successful deployments\n\n🌟 **PORTFOLIO LENGKAP:** renatahenessa.com\n\n📞 **HUBUNGI LANGSUNG:**\n+62 822-1049-3145\n\n💬 **KONSULTASI GRATIS TERSEDIA!**\nKetik \"admin\" untuk berbicara langsung\n\n✨ Sistem AI akan kembali normal segera!`;
}

/**
 * Wrapper function to ensure no message goes unanswered with portfolio showcase
 */
function ensureResponseWithPortfolio(message, conversation, contactId) {
    try {
        // Try intelligent fallback first
        return generateIntelligentFallback(message, conversation, null);
    } catch (error) {
        console.error('🚨 Intelligent fallback failed:', error.message);
        try {
            // Try emergency fallback
            return generateEmergencyFallback(message, conversation);
        } catch (error2) {
            console.error('🚨 Emergency fallback failed:', error2.message);
            // Ultimate fallback - this should never fail and always shows portfolio
            return generateUltimateFallback(message);
        }
    }
}

/**
 * Enhanced error handler that wraps the main function to guarantee response
 */
function safeRunAiAssistant(message, contactId = null, contactName = null) {
    try {
        return runAiAssistant(message, contactId, contactName);
    } catch (criticalError) {
        console.error('🚨 CRITICAL SYSTEM FAILURE:', criticalError.message);
        console.log('🛡️ Activating ultimate safety net...');
        
        // Ensure user always gets a response with full portfolio
        const safetyResponse = ensureResponseWithPortfolio(message, null, contactId);
        
        // Try to save to conversation if possible
        if (contactId && conversationManager) {
            try {
                conversationManager.addMessage(contactId, safetyResponse, false);
            } catch (saveError) {
                console.error('Could not save safety response:', saveError.message);
            }
        }
        
        return safetyResponse;
    }
}

module.exports = {
    runAiAssistant: safeRunAiAssistant, // Export the safe wrapper instead
    conversationManager,
    handleSilentProspects,
    getComprehensiveInsights,
    ensureResponseWithPortfolio, // Export for external use
    generateUltimateFallback, // Export for testing
    // Export engines for external use
    psychEngine,
    persuasionEngine,
    ctaEngine,
    zeroLossEngine
};
