const dotenv = require('dotenv');
dotenv.config();

// Helper function to normalize phone numbers (same as in bot)
function normalizePhoneNumber(phone) {
  if (!phone) return '';

  // Remove all non-numeric characters
  let cleaned = phone.toString().replace(/\D/g, '');

  // Handle Indonesian phone numbers - normalize to international format
  if (cleaned.startsWith('0')) {
    cleaned = '62' + cleaned.substring(1);
  } else if (cleaned.startsWith('8')) {
    cleaned = '62' + cleaned;
  } else if (!cleaned.startsWith('62')) {
    cleaned = '62' + cleaned;
  }

  return cleaned;
}

// Helper function to check if a contact is admin (same as in bot)
function isAdminContact(contactId) {
  const nomorPengguna = contactId.replace("@c.us", "");
  const normalizedUser = normalizePhoneNumber(nomorPengguna);
  const normalizedAdmin = normalizePhoneNumber(process.env.ADMIN_CONTACT);

  console.log(`🔍 Admin check: User "${nomorPengguna}" → "${normalizedUser}" vs Admin "${process.env.ADMIN_CONTACT}" → "${normalizedAdmin}"`);

  return normalizedUser === normalizedAdmin;
}

/**
 * Test script to verify admin number recognition logic
 */
function testAdminLogic() {
    console.log('🧪 Testing UPDATED Admin Number Recognition Logic\n');

    // Get admin contact from environment
    const adminContact = process.env.ADMIN_CONTACT;
    console.log(`📋 ADMIN_CONTACT from .env: "${adminContact}"`);

    // Normalize admin contact
    const normalizedAdminContact = normalizePhoneNumber(adminContact);
    console.log(`🧹 Normalized admin contact: "${normalizedAdminContact}"`);

    // Test scenarios
    const testCases = [
        {
            name: "Direct admin number",
            contactId: "<EMAIL>",
            expected: true
        },
        {
            name: "Admin number with country code",
            contactId: "<EMAIL>",
            expected: true
        },
        {
            name: "Bot's own number",
            contactId: "<EMAIL>",
            expected: false
        },
        {
            name: "Random number",
            contactId: "<EMAIL>",
            expected: false
        }
    ];

    console.log('\n🔍 Testing different contact ID formats with NEW logic:\n');

    testCases.forEach((testCase, index) => {
        console.log(`Test ${index + 1}: ${testCase.name}`);
        console.log(`  Contact ID: ${testCase.contactId}`);

        // Use the new admin check function
        const isAdmin = isAdminContact(testCase.contactId);
        console.log(`  Expected: ${testCase.expected}`);
        console.log(`  ✅ ${isAdmin === testCase.expected ? 'PASS' : '❌ FAIL'}\n`);
    });

    // Additional test: Check phone number normalization
    console.log('📱 Testing phone number normalization with NEW logic:\n');

    const phoneNumbers = [
        "089519015762",
        "6289519015762",
        "+6289519015762",
        "0895-1901-5762",
        "62 895 1901 5762"
    ];

    phoneNumbers.forEach(phone => {
        const normalized = normalizePhoneNumber(phone);
        console.log(`  "${phone}" → "${normalized}" → Match: ${normalized === normalizedAdminContact}`);
    });
}

// Run the test
testAdminLogic();
