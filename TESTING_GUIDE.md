# WhatsApp Chatbot Testing & Documentation Guide

## 📋 Table of Contents
- [Environment Variables](#environment-variables)
- [Phone Number Configuration](#phone-number-configuration)
- [Admin Commands](#admin-commands)
- [Testing Procedures](#testing-procedures)
- [Setup Instructions](#setup-instructions)
- [Usage Examples](#usage-examples)
- [Troubleshooting](#troubleshooting)

## 🔧 Environment Variables

### Required Configuration (.env file)

```env
# AI Configuration (Generic - supports any AI provider)
AI_API_KEY=your_ai_api_key_here

# WhatsApp Bot Configuration
WHATSAPP_BOT_NUMBER=+62 822-1049-3145

# Admin Contact Information (for admin commands)
ADMIN_CONTACT=+62 822-1049-3145

# Testing Configuration
TESTING_PHONE_NUMBER=+62 896-9621-7565

# Lead Storage
LEADS_FILE=leads.json
```

### Variable Descriptions

| Variable | Purpose | Format | Example |
|----------|---------|--------|---------|
| `AI_API_KEY` | API key for AI service (Google Gemini) | String | `AIzaSyCpS-iq3rhXuem54paaCtYxXlTLJ7hXvcs` |
| `WHATSAPP_BOT_NUMBER` | Bot's own WhatsApp number for consultations | +62 format with dashes | `+62 822-1049-3145` |
| `ADMIN_CONTACT` | Phone number with admin privileges | +62 format with dashes | `+62 822-1049-3145` |
| `TESTING_PHONE_NUMBER` | Phone number for testing blast functionality | +62 format with dashes | `+62 896-9621-7565` |
| `LEADS_FILE` | File to store lead data | Filename | `leads.json` |

## 📱 Phone Number Configuration

### Standard Format
All phone numbers in the system use the Indonesian international format:
- **Format**: `+62 XXX-XXXX-XXXX`
- **Example**: `+62 896-9621-7565`

### Phone Number Roles

1. **Admin Contact** (`ADMIN_CONTACT`): 
   - Has access to all admin commands
   - Can execute test blasts and view analytics
   - Currently: `+62 822-1049-3145`

2. **WhatsApp Bot Number** (`WHATSAPP_BOT_NUMBER`):
   - Bot's own WhatsApp number used in marketing messages
   - Contact number shown to clients for consultations
   - Currently: `+62 822-1049-3145`

3. **Testing Phone Number** (`TESTING_PHONE_NUMBER`):
   - Used for testing blast functionality
   - Receives test messages during development
   - Currently: `+62 896-9621-7565`

## 🔐 Admin Commands

### Available Commands (Admin Only)

| Command | Description | Usage | Response |
|---------|-------------|-------|----------|
| `test-blast` | Send test message to testing number | Send "test-blast" | Sends test message and provides instructions |
| `blast-bisnis` | Start business outreach blast | Send "blast-bisnis" | Initiates mass messaging to businesses |
| `analytics` or `stats-analytics` | View analytics report | Send "analytics" | Shows conversation and conversion statistics |
| `export-analytics` | Export analytics to CSV | Send "export-analytics" | Creates CSV files with analytics data |
| `stats-lead` | View lead statistics | Send "stats-lead" | Shows lead generation statistics |

### Admin Authorization
- Only the phone number configured in `ADMIN_CONTACT` can execute admin commands
- The system normalizes phone numbers for comparison (removes spaces, dashes, etc.)
- Unauthorized users receive "🚫 Maaf, Anda tidak memiliki izin" message

## 🧪 Testing Procedures

### 1. Test Blast Functionality

**Step 1: Initiate Test Blast**
```
Admin sends: test-blast
```

**Step 2: System Response**
- Bot sends test message to `TESTING_PHONE_NUMBER`
- Admin receives confirmation with statistics

**Step 3: Test Response Handling**
Send replies from testing number:
- `"tertarik"` - Tests positive response handling
- `"tidak"` - Tests negative response handling  
- Any other message - Tests neutral response handling

### 2. Admin Command Testing

**Test Admin Recognition:**
```bash
node test_admin_logic.js
```

**Test Different Phone Formats:**
- `089519015762` (local format)
- `6289519015762` (international without +)
- `+6289519015762` (full international)
- `0895-1901-5762` (local with dashes)
- `62 895 1901 5762` (international with spaces)

### 3. Analytics Testing

**Generate Test Data:**
1. Send `test-blast` command
2. Reply to test messages with different responses
3. Send `analytics` command to view results
4. Send `export-analytics` to generate CSV files

## 🚀 Setup Instructions

### 1. Environment Setup
```bash
# Clone the repository
git clone [repository-url]
cd whatsapp-chatbot

# Install dependencies
npm install

# Configure environment variables
cp .env.example .env
# Edit .env with your configuration
```

### 2. Required Dependencies
```json
{
  "whatsapp-web.js": "^1.31.0",
  "qrcode-terminal": "^0.12.0",
  "dotenv": "^16.0.0",
  "fs": "built-in",
  "path": "built-in"
}
```

### 3. File Structure
```
project/
├── src/
│   ├── index.js              # Main bot logic
│   ├── ai_assistant.js       # AI integration
│   ├── blast_manager.js      # Blast functionality
│   ├── lead_manager.js       # Lead management
│   └── analytics_manager.js  # Analytics tracking
├── test_blast.js            # Test blast functionality
├── test_admin_logic.js      # Admin testing
├── test_business_data.json  # Test data
├── .env                     # Environment variables
└── TESTING_GUIDE.md        # This documentation
```

## 💡 Usage Examples

### Example 1: Running Test Blast
```
Admin (from +62 822-1049-3145): test-blast

Bot Response:
🚀 Memulai WhatsApp blast ke bisnis-bisnis...
Proses ini akan mengirim pesan penawaran layanan...

[After completion]
🎉 Test blast selesai!
✅ Berhasil dikirim: 1
❌ Gagal: 0

Sekarang coba balas pesan yang diterima di +62 896-9621-7565 dengan:
• "tertarik" - untuk test respon positif
• "tidak" - untuk test respon negatif  
• pesan lain - untuk test respon netral
```

### Example 2: Viewing Analytics
```
Admin: analytics

Bot Response:
📊 ANALYTICS REPORT

📈 Overview:
• Total Conversations: 15
• Converted: 3
• Conversion Rate: 20.0%
• Active Conversations: 2
...
```

## 🔧 Troubleshooting

### Common Issues

1. **Admin Commands Not Working**
   - Check `ADMIN_CONTACT` in .env file
   - Verify phone number format matches exactly
   - Run `test_admin_logic.js` to debug

2. **Test Blast Fails**
   - Ensure `TESTING_PHONE_NUMBER` is set correctly
   - Check `test_business_data.json` has valid data
   - Verify WhatsApp client is connected

3. **Phone Number Format Issues**
   - Use consistent `+62 XXX-XXXX-XXXX` format
   - System auto-normalizes for comparison
   - Check normalization logic in `normalizePhoneNumber()`

### Debug Commands
```bash
# Test admin logic
node test_admin_logic.js

# Check environment variables
node -e "require('dotenv').config(); console.log(process.env)"

# Validate test data
node -e "console.log(JSON.parse(require('fs').readFileSync('test_business_data.json')))"
```

### Log Analysis
- Check console output for admin recognition logs
- Look for "🔍 Admin check:" messages
- Verify normalized phone number comparisons

## 📞 Support Contacts

- **Technical Issues**: Check logs and run debug commands
- **Business Inquiries**: Contact `WHATSAPP_BOT_NUMBER`
- **Admin Access**: Verify `ADMIN_CONTACT` configuration

---

*Last Updated: July 2025*
*Version: 2.0*
