# WhatsApp Chatbot Configuration Guide

## Overview
This guide explains how to configure the WhatsApp chatbot for your business. The chatbot has been made generic and configurable to work for any business consultation service.

## Environment Configuration

### Required Environment Variables (.env file)

```env
# AI Configuration (Generic - supports any AI provider)
AI_API_KEY=your_ai_api_key_here

# WhatsApp Bot Configuration
WHATSAPP_BOT_NUMBER=+62 xxx-xxxx-xxxx

# Admin Contact Information
ADMIN_CONTACT=your_admin_number

# Lead Storage
LEADS_FILE=leads.json
```

### Configuration Details

1. **AI_API_KEY**: Your AI provider API key (currently supports Google Gemini)
2. **WHATSAPP_BOT_NUMBER**: The bot's own WhatsApp number shown to clients for consultations
3. **ADMIN_CONTACT**: The number that has admin privileges for bot management
4. **LEADS_FILE**: File path for storing lead data

## Key Features Implemented

### 1. Content Filtering System
- Automatically detects inappropriate topics (politics, sexual content, violence, drugs, gambling)
- Redirects users back to business consultation topics
- Maintains professional focus at all times

### 2. Generic AI Branding
- Removed all "Gemini" references from user-facing messages
- Uses generic terms like "AI assistant" and "chatbot"
- Professional business consultation branding

### 3. Enhanced Call-to-Actions (CTAs)
- Every AI response includes business consultation CTA
- Consistent messaging directing users to business services
- Clear contact information and next steps

### 4. Professional Message Templates
- Updated blast message templates with better CTAs
- Removed hardcoded contact numbers
- Focus on ROI and business value proposition

### 5. Configurable Contact Information
- All contact numbers now use environment variables
- Easy to customize for different businesses
- No hardcoded phone numbers in the code

## Business Focus Areas

The chatbot is configured to focus on:
- Website Development & E-commerce
- Mobile App Development
- Digital Marketing & SEO
- Data Analytics & Business Intelligence
- Custom Business System Development
- Business Consultation Services

## Content Filtering Keywords

### Inappropriate Topics (Automatically Redirected)
- Political discussions
- Sexual content
- Violence-related topics
- Drug-related content
- Gambling discussions

### Encouraged Topics
- Website development
- Mobile applications
- Digital marketing
- Business systems
- Data analysis
- Business consultation

## Usage Commands

### For Admin Users (ADMIN_CONTACT)
- `test-blast` - Test blast functionality (sends to TESTING_PHONE_NUMBER)
- `blast-bisnis` - Start business outreach blast
- `analytics` or `stats-analytics` - View comprehensive analytics report
- `export-analytics` - Export analytics data to CSV files
- `stats-lead` - View lead statistics

> 📖 **For detailed testing procedures, see [TESTING_GUIDE.md](TESTING_GUIDE.md)**

### For All Users
- `admin` - Connect with human consultant
- `layanan` - View service offerings
- `harga` - View pricing packages
- `kontak` - Get contact information
- Any other message - Processed by AI with business focus

## Deployment Steps

1. Update `.env` file with your configuration
2. Ensure business data Excel file is in place
3. Run `npm run setup` to process business data
4. Start the bot with `npm start`
5. Scan QR code with WhatsApp

## Customization Tips

1. **Branding**: Update company name and services in message templates
2. **Contact Info**: Set WHATSAPP_BOT_NUMBER to your bot's WhatsApp number
3. **Services**: Modify service descriptions in `src/cek_business.json`
4. **AI Prompt**: Adjust business focus in `src/ai_assistant.js`
5. **Blast Messages**: Customize templates in `src/blast_manager.js`

## Professional Features

- Content filtering for inappropriate topics
- Business-focused conversation flow
- Lead qualification and management
- Automated business outreach
- Professional response templates
- Configurable contact information
- ROI-focused messaging

This configuration ensures the chatbot maintains professional standards while being easily deployable for any business consultation service.
