/**
 * Simple Test for Psychological Engagement System
 */

const { runAiAssistant, getComprehensiveInsights } = require('./src/ai_assistant');

async function simpleTest() {
    console.log('🧪 Running Simple Psychological Engagement Test...\n');
    
    const testContactId = 'test_user_' + Date.now();
    const testContactName = 'Test User';
    
    // Test 1: Frustrated client
    console.log('📋 Test 1: Frustrated Client');
    console.log('User: "Sistem saya sangat lambat dan bikin frustasi!"');
    
    const response1 = await runAiAssistant(
        "Sistem saya sangat lambat dan bikin frustasi!", 
        testContactId, 
        testContactName
    );
    
    console.log('AI Response:', response1);
    console.log('');
    
    // Test 2: Follow-up with interest
    console.log('📋 Test 2: Showing Interest');
    console.log('User: "Wah menarik! Bisa cerita lebih detail?"');
    
    const response2 = await runAiAssistant(
        "Wah menarik! Bisa cerita lebih detail?", 
        testContactId, 
        testContactName
    );
    
    console.log('AI Response:', response2);
    console.log('');
    
    // Test 3: Budget objection
    console.log('📋 Test 3: Budget Objection');
    console.log('User: "Ini terlalu mahal untuk budget saya"');
    
    const response3 = await runAiAssistant(
        "Ini terlalu mahal untuk budget saya", 
        testContactId, 
        testContactName
    );
    
    console.log('AI Response:', response3);
    console.log('');
    
    // Test 4: Direct rejection
    console.log('📋 Test 4: Direct Rejection');
    console.log('User: "Tidak, saya tidak tertarik"');
    
    const response4 = await runAiAssistant(
        "Tidak, saya tidak tertarik", 
        testContactId, 
        testContactName
    );
    
    console.log('AI Response:', response4);
    console.log('');
    
    // Get comprehensive insights
    console.log('📊 Comprehensive Insights:');
    const insights = getComprehensiveInsights(testContactId);
    
    console.log('Conversation Stage:', insights.conversation.stage);
    console.log('Emotional Journey:', insights.psychological.emotionalJourney.map(e => e.emotion));
    console.log('Objection Patterns:', insights.psychological.objectionPatterns.map(o => o.type));
    console.log('Engagement Level:', insights.psychological.engagementLevel);
    console.log('Conversion Readiness:', insights.psychological.conversionReadiness);
    console.log('Recommended Actions:', insights.recommendedActions.map(a => a.action));
    
    console.log('\n✅ Simple test completed!');
}

// Run the test
if (require.main === module) {
    simpleTest().catch(console.error);
}

module.exports = { simpleTest };
