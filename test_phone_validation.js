const BusinessDataManager = require('./src/business_data');
const BlastManager = require('./src/blast_manager');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Test phone number validation
function testPhoneValidation() {
    console.log('🧪 Testing Phone Number Validation\n');
    
    const businessDataManager = new BusinessDataManager();
    
    const testNumbers = [
        '***********',           // Local format
        '6281234567890',         // International without +
        '+6281234567890',        // Full international
        '0812-3456-7890',        // Local with dashes
        '62 812 3456 7890',      // International with spaces
        '8123456789',            // Without leading 0
        '089696217565',          // Test number from env
        process.env.TESTING_PHONE_NUMBER || '+62 896-9621-7565',
        '628969621756',          // Cleaned test number
        '0896-9621-7565',        // Test number local format
        '123456789',             // Invalid format
        '',                      // Empty
        null,                    // Null
        undefined                // Undefined
    ];
    
    console.log('📞 Testing phone number cleaning and validation:\n');
    
    testNumbers.forEach((phone, index) => {
        try {
            const cleaned = businessDataManager.cleanPhoneNumber(phone);
            const contactId = cleaned ? `${cleaned}@c.us` : 'N/A';
            
            console.log(`${index + 1}. Input: "${phone}"`);
            console.log(`   Cleaned: "${cleaned}"`);
            console.log(`   Contact ID: "${contactId}"`);
            console.log(`   Valid: ${cleaned && cleaned.length >= 11 && cleaned.startsWith('62') ? '✅' : '❌'}\n`);
        } catch (error) {
            console.log(`${index + 1}. Input: "${phone}"`);
            console.log(`   Error: ${error.message}\n`);
        }
    });
}

// Test blast manager validation
function testBlastManagerValidation() {
    console.log('🚀 Testing Blast Manager Validation\n');
    
    // Create a mock WhatsApp client for testing
    const mockClient = {
        info: { wid: 'test' },
        sendMessage: async (contactId, message) => {
            console.log(`   Mock send to ${contactId}: ${message.substring(0, 30)}...`);
            return Promise.resolve();
        },
        getChatById: async (contactId) => {
            return {
                sendMessage: async (message) => {
                    console.log(`   Mock chat send: ${message.substring(0, 30)}...`);
                    return Promise.resolve();
                }
            };
        },
        getContactById: async (contactId) => {
            return { id: contactId };
        }
    };
    
    const blastManager = new BlastManager(mockClient);
    
    const testNumbers = [
        '089696217565',          // Test number
        '***********',           // Regular number
        '6281234567890',         // Already formatted
        'invalid',               // Invalid
        ''                       // Empty
    ];
    
    console.log('📱 Testing blast manager phone validation:\n');
    
    testNumbers.forEach((phone, index) => {
        try {
            const validation = blastManager.validatePhoneNumber(phone);
            
            console.log(`${index + 1}. Testing: "${phone}"`);
            console.log(`   Original: ${validation.original}`);
            console.log(`   Cleaned: ${validation.cleaned}`);
            console.log(`   Valid: ${validation.isValid ? '✅' : '❌'}`);
            console.log(`   Contact ID: ${validation.contactId || 'N/A'}\n`);
        } catch (error) {
            console.log(`${index + 1}. Testing: "${phone}"`);
            console.log(`   Error: ${error.message}\n`);
        }
    });
}

// Test message templates
function testMessageTemplates() {
    console.log('💬 Testing Message Templates\n');
    
    const mockClient = { info: { wid: 'test' } };
    const blastManager = new BlastManager(mockClient);
    
    const templates = blastManager.getMessageTemplates();
    
    console.log('📝 Available message templates:\n');
    
    Object.keys(templates).forEach((templateName, index) => {
        const template = templates[templateName];
        const personalized = template.replace(/{{business_name}}/g, 'Test Business');
        
        console.log(`${index + 1}. Template: ${templateName}`);
        console.log(`   Raw: ${template}`);
        console.log(`   Personalized: ${personalized}\n`);
    });
}

// Main test function
function runTests() {
    console.log('🧪 WhatsApp Blast System Tests\n');
    console.log('=' * 50 + '\n');
    
    try {
        testPhoneValidation();
        console.log('=' * 50 + '\n');
        
        testBlastManagerValidation();
        console.log('=' * 50 + '\n');
        
        testMessageTemplates();
        console.log('=' * 50 + '\n');
        
        console.log('✅ All tests completed successfully!');
        console.log('\n📋 Next steps:');
        console.log('1. Start the WhatsApp bot: npm start');
        console.log('2. Send "test-phone 089696217565" from admin number');
        console.log('3. Send "test-blast" to test the blast functionality');
        console.log('4. Check logs for any errors or issues');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.error(error.stack);
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    runTests();
}

module.exports = {
    testPhoneValidation,
    testBlastManagerValidation,
    testMessageTemplates,
    runTests
};
