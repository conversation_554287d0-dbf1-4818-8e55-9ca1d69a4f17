const bodyParser = require('body-parser');
const fs = require('fs');
// Baca data dari file JSON

const cekLocalMessage = async (message) => {
    try {
        const responses = await JSON.parse(fs.readFileSync('./src/cek_business.json', 'utf8'));
        // console.log(responses);
        const response = responses.find(r => r.nomor === message);
        return response;
    } catch (error) {
        console.log('Local message file not found, using AI Assistant only');
        return null;
    }
}

module.exports.cekLocalMessage = cekLocalMessage;
