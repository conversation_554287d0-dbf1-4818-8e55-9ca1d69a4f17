/**
 * Test Suite for Advanced Psychological Engagement System
 * Tests all psychological techniques and persuasive conversation strategies
 */

const { runAiAssistant, handleSilentProspects, getComprehensiveInsights } = require('./src/ai_assistant');

// Test scenarios for different psychological states
const TEST_SCENARIOS = {
    frustrated_client: {
        messages: [
            "Sistem saya sekarang sangat lambat dan bikin frustasi!",
            "Saya sudah capek dengan masalah ini terus-terusan",
            "Ini sangat mengganggu produktivitas tim saya"
        ],
        expectedTechniques: ['feel_felt_found', 'empathy', 'solution_focus']
    },
    
    excited_prospect: {
        messages: [
            "Wah ini terdengar amazing!",
            "Saya sangat tertarik dengan solusi ini",
            "Kapan bisa kita mulai implementasi?"
        ],
        expectedTechniques: ['momentum_building', 'urgency', 'assumptive_close']
    },
    
    skeptical_buyer: {
        messages: [
            "Saya tidak yakin ini akan work untuk bisnis saya",
            "Apakah ada guarantee kalau ini berhasil?",
            "Bagaimana saya tahu ini tidak buang-buang uang?"
        ],
        expectedTechniques: ['social_proof', 'authority', 'risk_reversal']
    },
    
    budget_objection: {
        messages: [
            "Ini terlalu mahal untuk budget saya",
            "Saya tidak punya budget sebesar itu",
            "Apakah ada opsi yang lebih murah?"
        ],
        expectedTechniques: ['roi_focus', 'cost_of_inaction', 'alternative_close']
    },
    
    time_objection: {
        messages: [
            "Saya terlalu sibuk untuk ini sekarang",
            "Tidak ada waktu untuk implementasi",
            "Mungkin nanti saja kalau sudah tidak sibuk"
        ],
        expectedTechniques: ['time_value', 'automation_benefits', 'urgency']
    },
    
    silent_prospect: {
        daysSilent: 5,
        lastInteraction: 'showed_interest',
        expectedTechniques: ['re_engagement', 'curiosity_gap', 'value_add']
    }
};

/**
 * Test psychological engagement with different scenarios
 */
async function testPsychologicalEngagement() {
    console.log('🧠 Testing Advanced Psychological Engagement System...\n');
    
    const results = {
        passed: 0,
        failed: 0,
        details: []
    };

    for (const [scenarioName, scenario] of Object.entries(TEST_SCENARIOS)) {
        console.log(`📋 Testing scenario: ${scenarioName}`);
        
        try {
            if (scenario.messages) {
                // Test conversation-based scenarios
                const testResult = await testConversationScenario(scenarioName, scenario);
                results.details.push(testResult);
                
                if (testResult.success) {
                    results.passed++;
                    console.log(`✅ ${scenarioName}: PASSED`);
                } else {
                    results.failed++;
                    console.log(`❌ ${scenarioName}: FAILED - ${testResult.reason}`);
                }
            } else if (scenario.daysSilent) {
                // Test silent prospect scenarios
                const testResult = await testSilentProspectScenario(scenarioName, scenario);
                results.details.push(testResult);
                
                if (testResult.success) {
                    results.passed++;
                    console.log(`✅ ${scenarioName}: PASSED`);
                } else {
                    results.failed++;
                    console.log(`❌ ${scenarioName}: FAILED - ${testResult.reason}`);
                }
            }
        } catch (error) {
            results.failed++;
            results.details.push({
                scenario: scenarioName,
                success: false,
                reason: `Error: ${error.message}`,
                response: null
            });
            console.log(`❌ ${scenarioName}: ERROR - ${error.message}`);
        }
        
        console.log(''); // Empty line for readability
    }

    return results;
}

/**
 * Test conversation-based scenarios
 */
async function testConversationScenario(scenarioName, scenario) {
    const testContactId = `test_${scenarioName}_${Date.now()}`;
    const testContactName = `Test ${scenarioName}`;
    
    let lastResponse = '';
    
    // Simulate conversation
    for (const message of scenario.messages) {
        lastResponse = await runAiAssistant(message, testContactId, testContactName);
        
        // Small delay to simulate real conversation
        await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    // Analyze the response for expected techniques
    const analysis = analyzeResponseForTechniques(lastResponse, scenario.expectedTechniques);
    
    // Get comprehensive insights
    const insights = getComprehensiveInsights(testContactId);
    
    return {
        scenario: scenarioName,
        success: analysis.techniquesFound >= scenario.expectedTechniques.length * 0.6, // 60% threshold
        reason: analysis.techniquesFound < scenario.expectedTechniques.length * 0.6 ? 
                `Only found ${analysis.techniquesFound}/${scenario.expectedTechniques.length} expected techniques` : 
                'All key techniques detected',
        response: lastResponse,
        analysis: analysis,
        insights: insights,
        techniquesDetected: analysis.detectedTechniques
    };
}

/**
 * Test silent prospect scenarios
 */
async function testSilentProspectScenario(scenarioName, scenario) {
    // This would normally be tested with actual silent prospects
    // For testing, we'll simulate the scenario
    
    const silentProspects = await handleSilentProspects();
    
    return {
        scenario: scenarioName,
        success: true, // Simplified for demo
        reason: 'Silent prospect handling system operational',
        response: silentProspects.length > 0 ? silentProspects[0].message : 'No silent prospects found',
        analysis: {
            prospectsFound: silentProspects.length,
            reEngagementMessages: silentProspects.map(p => p.message)
        }
    };
}

/**
 * Analyze response for psychological techniques
 */
function analyzeResponseForTechniques(response, expectedTechniques) {
    const detectedTechniques = [];
    let techniquesFound = 0;
    
    const responseText = response.toLowerCase();
    
    // Check for specific technique indicators (more comprehensive)
    const techniqueIndicators = {
        feel_felt_found: ['understand', 'feel', 'felt', 'found', 'others have', 'mengerti', 'merasakan', 'klien lain'],
        empathy: ['i understand', 'i can imagine', 'that must be', 'i hear you', 'saya mengerti', 'pasti', 'kebayang'],
        social_proof: ['other clients', 'similar businesses', 'clients have', 'success stories', 'klien', 'bisnis lain', 'berhasil'],
        urgency: ['limited', 'only', 'expires', 'last chance', 'hurry', 'terbatas', 'segera', 'cepat'],
        scarcity: ['few slots', 'limited availability', 'exclusive', 'only for', 'slot terbatas', 'eksklusif'],
        authority: ['expert', 'experience', 'track record', 'proven', 'ahli', 'pengalaman', 'terbukti'],
        risk_reversal: ['guarantee', 'no risk', 'money back', 'trial', 'garansi', 'gratis', 'coba'],
        curiosity_gap: ['curious', 'interesting', 'something', 'secret', 'penasaran', 'menarik', 'ada yang'],
        roi_focus: ['roi', 'return', 'investment', 'save money', 'increase revenue', 'hemat', 'untung', 'efisien'],
        alternative_close: ['would you prefer', 'option a or b', 'which would', 'pilih', 'mau yang mana'],
        assumptive_close: ['when would', 'i\'ll schedule', 'let me book', 'kapan', 'saya jadwalkan'],
        solution_focus: ['solusi', 'solution', 'cara', 'bisa bantu', 'atasi'],
        momentum_building: ['amazing', 'excited', 'tertarik', 'bagus', 'mantap'],
        time_value: ['waktu', 'time', 'hemat waktu', 'save time', 'efisiensi'],
        automation_benefits: ['otomatis', 'automation', 'sistem', 'streamline'],
        cost_of_inaction: ['cost', 'biaya', 'rugi', 'kehilangan', 'tanpa sistem']
    };
    
    for (const technique of expectedTechniques) {
        const indicators = techniqueIndicators[technique] || [];
        const found = indicators.some(indicator => responseText.includes(indicator));
        
        if (found) {
            detectedTechniques.push(technique);
            techniquesFound++;
        }
    }
    
    // Check for general psychological engagement indicators
    const engagementIndicators = [
        'emoji usage', 'personal pronouns', 'questions', 'empathetic language',
        'action-oriented language', 'benefit statements', 'social proof elements'
    ];
    
    const hasEmojis = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]/u.test(response);
    const hasQuestions = response.includes('?');
    const hasPersonalPronouns = /\b(you|your|anda|kamu)\b/i.test(response);
    
    return {
        techniquesFound,
        detectedTechniques,
        engagementScore: calculateEngagementScore(response),
        hasEmojis,
        hasQuestions,
        hasPersonalPronouns,
        responseLength: response.length,
        persuasionElements: detectedTechniques.length
    };
}

/**
 * Calculate engagement score based on response characteristics
 */
function calculateEngagementScore(response) {
    let score = 0;
    
    // Length (optimal range 100-300 characters)
    const length = response.length;
    if (length >= 100 && length <= 300) score += 20;
    else if (length >= 50 && length <= 500) score += 10;
    
    // Emoji usage
    if (/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]/u.test(response)) score += 15;
    
    // Questions (engagement)
    if (response.includes('?')) score += 20;
    
    // Personal pronouns (connection)
    if (/\b(you|your|anda|kamu)\b/i.test(response)) score += 15;
    
    // Action words
    const actionWords = ['mari', 'ayo', 'yuk', 'let\'s', 'should', 'could', 'would'];
    if (actionWords.some(word => response.toLowerCase().includes(word))) score += 10;
    
    // Benefit statements
    const benefitWords = ['save', 'increase', 'improve', 'boost', 'enhance', 'optimize'];
    if (benefitWords.some(word => response.toLowerCase().includes(word))) score += 10;
    
    // Contact information (CTA)
    if (response.includes('📞') || response.includes('admin')) score += 10;
    
    return Math.min(100, score);
}

/**
 * Run comprehensive test suite
 */
async function runTestSuite() {
    console.log('🚀 Starting Comprehensive Psychological Engagement Test Suite\n');
    console.log('=' .repeat(60));
    
    const startTime = Date.now();
    
    try {
        const results = await testPsychologicalEngagement();
        
        const endTime = Date.now();
        const duration = (endTime - startTime) / 1000;
        
        console.log('📊 TEST RESULTS SUMMARY');
        console.log('=' .repeat(60));
        console.log(`✅ Passed: ${results.passed}`);
        console.log(`❌ Failed: ${results.failed}`);
        console.log(`📈 Success Rate: ${((results.passed / (results.passed + results.failed)) * 100).toFixed(1)}%`);
        console.log(`⏱️  Duration: ${duration.toFixed(2)} seconds`);
        console.log('');
        
        // Detailed results
        console.log('📋 DETAILED RESULTS');
        console.log('-' .repeat(60));
        
        results.details.forEach(detail => {
            console.log(`\n🔍 ${detail.scenario.toUpperCase()}`);
            console.log(`Status: ${detail.success ? '✅ PASSED' : '❌ FAILED'}`);
            console.log(`Reason: ${detail.reason}`);
            
            if (detail.analysis) {
                console.log(`Engagement Score: ${detail.analysis.engagementScore || 'N/A'}/100`);
                console.log(`Techniques Detected: ${detail.techniquesDetected ? detail.techniquesDetected.join(', ') : 'None'}`);
            }
            
            if (detail.response) {
                console.log(`Sample Response: "${detail.response.substring(0, 150)}..."`);
            }
        });
        
        console.log('\n' + '=' .repeat(60));
        console.log('🎉 Test Suite Completed Successfully!');
        
        return results;
        
    } catch (error) {
        console.error('💥 Test Suite Failed:', error.message);
        console.error(error.stack);
        return null;
    }
}

// Export for use in other modules
module.exports = {
    runTestSuite,
    testPsychologicalEngagement,
    analyzeResponseForTechniques,
    calculateEngagementScore
};

// Run tests if this file is executed directly
if (require.main === module) {
    runTestSuite().then(results => {
        if (results) {
            process.exit(results.failed === 0 ? 0 : 1);
        } else {
            process.exit(1);
        }
    });
}
