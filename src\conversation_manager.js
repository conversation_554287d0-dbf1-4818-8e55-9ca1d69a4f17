const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const PsychologicalEngagement = require('./psychological_engagement');

class ConversationManager {
    constructor() {
        this.conversationsFile = 'conversations.json';
        this.conversations = {};
        this.psychEngine = new PsychologicalEngagement();
        this.loadConversations();
        
        // Conversation stages
        this.STAGES = {
            INITIAL: 'initial',
            QUALIFICATION: 'qualification', 
            PROBLEM_DISCOVERY: 'problem_discovery',
            SOLUTION_PRESENTATION: 'solution_presentation',
            INTEREST_BUILDING: 'interest_building',
            DEAL_CLOSURE: 'deal_closure',
            CONVERTED: 'converted'
        };

        // Buying intent keywords
        this.BUYING_INTENT_KEYWORDS = [
            'deal', 'interested', 'yes', 'proceed', 'start', 'begin', 'hire', 'work together',
            'let\'s do it', 'sounds good', 'i want', 'need this', 'how much', 'price',
            'cost', 'budget', 'when can we start', 'timeline', 'proposal', 'quote'
        ];

        // Business problem keywords for categorization
        this.PROBLEM_CATEGORIES = {
            growth: ['growth', 'expand', 'scale', 'increase sales', 'more customers', 'revenue'],
            efficiency: ['slow', 'manual', 'time consuming', 'inefficient', 'automate', 'streamline'],
            technology: ['outdated', 'old system', 'no website', 'no app', 'digital transformation'],
            data: ['data', 'analytics', 'reports', 'insights', 'tracking', 'metrics'],
            competition: ['competitors', 'behind', 'losing customers', 'market share']
        };
    }

    /**
     * Load conversations from file
     */
    loadConversations() {
        try {
            if (fs.existsSync(this.conversationsFile)) {
                const data = fs.readFileSync(this.conversationsFile, 'utf8');
                this.conversations = JSON.parse(data);
            }
        } catch (error) {
            console.error('Error loading conversations:', error);
            this.conversations = {};
        }
    }

    /**
     * Save conversations to file
     */
    saveConversations() {
        try {
            fs.writeFileSync(this.conversationsFile, JSON.stringify(this.conversations, null, 2));
        } catch (error) {
            console.error('Error saving conversations:', error);
        }
    }

    /**
     * Initialize or get conversation for a contact
     */
    getConversation(contactId, contactName = null) {
        if (!this.conversations[contactId]) {
            this.conversations[contactId] = {
                id: uuidv4(),
                contactId: contactId,
                contactName: contactName,
                stage: this.STAGES.INITIAL,
                startTime: new Date().toISOString(),
                lastActivity: new Date().toISOString(),
                messages: [],
                painPoints: [],
                businessType: null,
                problemCategory: null,
                conversionProbability: 0,
                isConverted: false,
                handoffTriggered: false,
                // Psychological tracking
                emotionalJourney: [],
                objectionPatterns: [],
                engagementLevel: 0,
                conversionReadiness: 0,
                lastEmotionalState: 'neutral',
                objectionCount: 0,
                reEngagementAttempts: 0,
                daysSinceLastMessage: 0,
                psychologicalProfile: {
                    primaryConcerns: [],
                    motivationTriggers: [],
                    communicationStyle: 'neutral',
                    decisionMakingPattern: 'analytical'
                }
            };
            this.saveConversations();
        }
        return this.conversations[contactId];
    }

    /**
     * Add message to conversation
     */
    addMessage(contactId, message, isFromUser = true, stage = null) {
        const conversation = this.getConversation(contactId);
        
        conversation.messages.push({
            id: uuidv4(),
            message: message,
            isFromUser: isFromUser,
            timestamp: new Date().toISOString(),
            stage: stage || conversation.stage
        });

        conversation.lastActivity = new Date().toISOString();
        
        // Analyze message for business insights and psychological patterns
        if (isFromUser) {
            this.analyzeUserMessage(contactId, message);
            this.updatePsychologicalProfile(contactId, message);
        }

        this.saveConversations();
        return conversation;
    }

    /**
     * Analyze user message for business insights
     */
    analyzeUserMessage(contactId, message) {
        const conversation = this.conversations[contactId];
        const lowerMessage = message.toLowerCase();

        // Check for buying intent
        const hasBuyingIntent = this.BUYING_INTENT_KEYWORDS.some(keyword => 
            lowerMessage.includes(keyword.toLowerCase())
        );

        if (hasBuyingIntent && !conversation.isConverted) {
            conversation.conversionProbability = Math.min(100, conversation.conversionProbability + 30);
            
            // If high probability and not already converted, trigger handoff
            if (conversation.conversionProbability >= 70 && !conversation.handoffTriggered) {
                conversation.stage = this.STAGES.DEAL_CLOSURE;
                conversation.handoffTriggered = true;
            }
        }

        // Categorize business problems
        for (const [category, keywords] of Object.entries(this.PROBLEM_CATEGORIES)) {
            if (keywords.some(keyword => lowerMessage.includes(keyword))) {
                if (!conversation.problemCategory) {
                    conversation.problemCategory = category;
                }
                
                // Extract pain points
                if (!conversation.painPoints.includes(message)) {
                    conversation.painPoints.push(message);
                }
                break;
            }
        }

        this.saveConversations();
    }

    /**
     * Update psychological profile based on user message
     */
    updatePsychologicalProfile(contactId, message) {
        const conversation = this.conversations[contactId];

        // Detect emotional state
        const emotionalState = this.psychEngine.detectEmotionalState(message);
        conversation.lastEmotionalState = emotionalState;
        conversation.emotionalJourney.push({
            emotion: emotionalState,
            timestamp: new Date().toISOString(),
            message: message.substring(0, 100) // Store snippet for context
        });

        // Check for objections
        const objection = this.psychEngine.handleObjection(message);
        if (objection) {
            conversation.objectionCount++;
            conversation.objectionPatterns.push({
                type: objection.type,
                timestamp: new Date().toISOString(),
                originalMessage: message.substring(0, 100)
            });
        }

        // Update engagement metrics
        const analysis = this.psychEngine.analyzeConversation(conversation.messages);
        conversation.engagementLevel = analysis.engagementLevel;
        conversation.conversionReadiness = analysis.conversionReadiness;

        // Update psychological profile
        this.updateCommunicationStyle(conversation, message);
        this.updateMotivationTriggers(conversation, emotionalState);
        this.updateDecisionMakingPattern(conversation, message);

        this.saveConversations();
    }

    /**
     * Update communication style based on message patterns
     */
    updateCommunicationStyle(conversation, message) {
        const messageLength = message.length;
        const hasEmojis = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]/u.test(message);
        const isDetailed = messageLength > 100;
        const isCasual = message.toLowerCase().includes('ya') || message.toLowerCase().includes('sih');

        if (isCasual || hasEmojis) {
            conversation.psychologicalProfile.communicationStyle = 'casual';
        } else if (isDetailed) {
            conversation.psychologicalProfile.communicationStyle = 'detailed';
        } else {
            conversation.psychologicalProfile.communicationStyle = 'formal';
        }
    }

    /**
     * Update motivation triggers based on emotional patterns
     */
    updateMotivationTriggers(conversation, emotionalState) {
        const triggers = conversation.psychologicalProfile.motivationTriggers;

        switch (emotionalState) {
            case 'frustrated':
                if (!triggers.includes('efficiency')) triggers.push('efficiency');
                break;
            case 'excited':
                if (!triggers.includes('growth')) triggers.push('growth');
                break;
            case 'worried':
                if (!triggers.includes('security')) triggers.push('security');
                break;
            case 'urgent':
                if (!triggers.includes('speed')) triggers.push('speed');
                break;
        }
    }

    /**
     * Update decision making pattern based on conversation behavior
     */
    updateDecisionMakingPattern(conversation, message) {
        const hasQuestions = message.includes('?');
        const mentionsData = message.toLowerCase().includes('data') || message.toLowerCase().includes('proof');
        const mentionsTeam = message.toLowerCase().includes('team') || message.toLowerCase().includes('discuss');

        if (mentionsData) {
            conversation.psychologicalProfile.decisionMakingPattern = 'analytical';
        } else if (mentionsTeam) {
            conversation.psychologicalProfile.decisionMakingPattern = 'collaborative';
        } else if (hasQuestions) {
            conversation.psychologicalProfile.decisionMakingPattern = 'inquisitive';
        } else {
            conversation.psychologicalProfile.decisionMakingPattern = 'intuitive';
        }
    }

    /**
     * Get psychological insights for a conversation
     */
    getPsychologicalInsights(contactId) {
        const conversation = this.conversations[contactId];
        if (!conversation) return null;

        return {
            currentEmotionalState: conversation.lastEmotionalState,
            emotionalJourney: conversation.emotionalJourney,
            objectionPatterns: conversation.objectionPatterns,
            engagementLevel: conversation.engagementLevel,
            conversionReadiness: conversation.conversionReadiness,
            psychologicalProfile: conversation.psychologicalProfile,
            recommendedApproach: this.getRecommendedApproach(conversation)
        };
    }

    /**
     * Get recommended psychological approach
     */
    getRecommendedApproach(conversation) {
        const profile = conversation.psychologicalProfile;
        const lastEmotion = conversation.lastEmotionalState;

        let approach = {
            technique: 'rapport_building',
            tone: 'warm',
            focus: 'relationship',
            urgency: 'low'
        };

        // Adjust based on emotional state
        if (lastEmotion === 'frustrated') {
            approach.technique = 'feel_felt_found';
            approach.focus = 'problem_solving';
        } else if (lastEmotion === 'excited') {
            approach.technique = 'momentum_building';
            approach.urgency = 'high';
        } else if (lastEmotion === 'worried') {
            approach.technique = 'authority_social_proof';
            approach.tone = 'reassuring';
        }

        // Adjust based on communication style
        if (profile.communicationStyle === 'casual') {
            approach.tone = 'friendly';
        } else if (profile.communicationStyle === 'formal') {
            approach.tone = 'professional';
        }

        // Adjust based on decision making pattern
        if (profile.decisionMakingPattern === 'analytical') {
            approach.focus = 'data_driven';
        } else if (profile.decisionMakingPattern === 'collaborative') {
            approach.focus = 'team_benefits';
        }

        return approach;
    }

    /**
     * Update conversation stage
     */
    updateStage(contactId, newStage) {
        const conversation = this.getConversation(contactId);
        conversation.stage = newStage;
        conversation.lastActivity = new Date().toISOString();
        this.saveConversations();
        return conversation;
    }

    /**
     * Mark conversation as converted
     */
    markAsConverted(contactId) {
        const conversation = this.getConversation(contactId);
        conversation.isConverted = true;
        conversation.stage = this.STAGES.CONVERTED;
        conversation.conversionTime = new Date().toISOString();
        this.saveConversations();
        return conversation;
    }

    /**
     * Mark conversation as blocked due to inappropriate content
     */
    markAsBlocked(contactId, reason = 'inappropriate_content') {
        const conversation = this.getConversation(contactId);
        conversation.isBlocked = true;
        conversation.blockReason = reason;
        conversation.blockTime = new Date().toISOString();
        conversation.stage = 'BLOCKED';
        this.saveConversations();
        
        // Log the blocking for security monitoring
        console.log(`🚫 CONVERSATION BLOCKED: ${contactId} - Reason: ${reason}`);
        console.log(`📅 Block Time: ${conversation.blockTime}`);
        
        return conversation;
    }

    /**
     * Check if conversation is blocked
     */
    isConversationBlocked(contactId) {
        const conversation = this.conversations[contactId];
        return conversation && conversation.isBlocked === true;
    }

    /**
     * Get conversation statistics
     */
    getStatistics() {
        const conversations = Object.values(this.conversations);
        const total = conversations.length;
        const converted = conversations.filter(c => c.isConverted).length;
        const byStage = {};
        
        Object.values(this.STAGES).forEach(stage => {
            byStage[stage] = conversations.filter(c => c.stage === stage).length;
        });

        const avgConversionTime = this.calculateAverageConversionTime(conversations);
        
        return {
            total,
            converted,
            conversionRate: total > 0 ? ((converted / total) * 100).toFixed(2) : 0,
            byStage,
            avgConversionTime
        };
    }

    /**
     * Calculate average conversion time
     */
    calculateAverageConversionTime(conversations) {
        const convertedConversations = conversations.filter(c => c.isConverted && c.conversionTime);
        
        if (convertedConversations.length === 0) return 0;

        const totalTime = convertedConversations.reduce((sum, conv) => {
            const start = new Date(conv.startTime);
            const end = new Date(conv.conversionTime);
            return sum + (end - start);
        }, 0);

        return Math.round(totalTime / convertedConversations.length / (1000 * 60)); // in minutes
    }

    /**
     * Export conversations to CSV
     */
    exportToCSV() {
        const headers = [
            'Contact ID', 'Contact Name', 'Stage', 'Start Time', 'Last Activity', 
            'Business Type', 'Problem Category', 'Pain Points', 'Conversion Probability',
            'Is Converted', 'Conversion Time', 'Message Count'
        ];
        
        const csvData = [headers.join(',')];
        
        Object.values(this.conversations).forEach(conv => {
            const painPoints = conv.painPoints.join('; ').replace(/"/g, '""');
            const row = [
                conv.contactId,
                `"${conv.contactName || ''}"`,
                conv.stage,
                conv.startTime,
                conv.lastActivity,
                `"${conv.businessType || ''}"`,
                conv.problemCategory || '',
                `"${painPoints}"`,
                conv.conversionProbability,
                conv.isConverted,
                conv.conversionTime || '',
                conv.messages.length
            ];
            csvData.push(row.join(','));
        });

        return csvData.join('\n');
    }
}

module.exports = ConversationManager;
