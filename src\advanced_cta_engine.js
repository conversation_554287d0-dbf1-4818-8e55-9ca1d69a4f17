/**
 * Advanced CTA Strategy Engine
 * Implements multiple CTA approaches with psychological triggers
 */

class AdvancedCTAEngine {
    constructor() {
        // CTA types with psychological triggers
        this.CTA_TYPES = {
            URGENCY: 'urgency',
            CURIOSITY: 'curiosity', 
            BENEFIT_FOCUSED: 'benefit_focused',
            FOMO: 'fomo',
            SOCIAL_PROOF: 'social_proof',
            AUTHORITY: 'authority',
            SCARCITY: 'scarcity',
            RISK_REVERSAL: 'risk_reversal'
        };

        // Professional consultation CTAs
        this.URGENCY_CTAS = [
            "Kapan waktu yang tepat untuk diskusi teknis lebih mendalam?",
            "Bagaimana kalau kita analisis kebutuhan sistem Anda secara detail?",
            "Saya bisa membantu evaluasi solusi yang paling sesuai untuk bisnis Anda",
            "Apakah Anda tertarik untuk konsultasi teknis lebih lanjut?",
            "Bagaimana kalau kita jadwalkan sesi analisis kebutuhan?"
        ];

        // Insight-based CTAs
        this.CURIOSITY_CTAS = [
            "Ada beberapa pendekatan data science yang mungkin relevan untuk kasus Anda",
            "Berdasarkan pengalaman saya, ada solusi yang mungkin belum terpikirkan",
            "Saya punya insights dari proyek serupa yang mungkin bermanfaat",
            "Ada metodologi yang bisa kita terapkan untuk optimisasi ini",
            "Mari kita eksplorasi opsi teknologi yang tersedia"
        ];

        // Value-focused CTAs
        this.BENEFIT_CTAS = [
            "Bagaimana kalau kita analisis ROI potensial untuk solusi ini?",
            "Mari diskusi cara mengoptimalkan operasional dengan pendekatan data-driven",
            "Kita bisa bahas strategi peningkatan efisiensi berdasarkan best practices",
            "Ada beberapa keuntungan terukur yang bisa kita evaluasi",
            "Bagaimana kalau kita hitung impact analysis untuk implementasi ini?"
        ];

        // Opportunity-focused CTAs
        this.FOMO_CTAS = [
            "Ada baiknya kita diskusi strategi implementasi yang optimal",
            "Bagaimana kalau kita evaluasi peluang optimisasi yang tersedia?",
            "Mari kita lihat opsi teknologi yang paling sesuai dengan kebutuhan Anda",
            "Sebaiknya kita pertimbangkan roadmap pengembangan yang strategis",
            "Bagaimana kalau kita manfaatkan momentum digitalisasi ini?"
        ];

        // Credibility-based CTAs
        this.SOCIAL_PROOF_CTAS = [
            "Berdasarkan hasil startup projects saya, ada metodologi yang terbukti efektif",
            "Pengalaman dengan multiple startup memberikan perspektif yang unik",
            "Dengan background Data Science, saya bisa share best practices yang terukur",
            "Portfolio saya menunjukkan hasil-hasil yang quantifiable",
            "Ada case studies dengan metrics yang bisa saya bagikan"
        ];

        // Expertise-based CTAs
        this.AUTHORITY_CTAS = [
            "Dengan background BSc Data Science, saya bisa memberikan analisis yang mendalam",
            "Pengalaman sebagai Associate Data Scientist memungkinkan saya memberikan insights yang valuable",
            "Ada beberapa best practices dari industri yang bisa kita terapkan",
            "Berdasarkan pengalaman startup projects, ini bisa menjadi solusi yang optimal",
            "Mau saya bantu berdasarkan pengalaman yang ada?"
        ];

        // Scarcity CTAs
        this.SCARCITY_CTAS = [
            "Mungkin ada baiknya kita diskusi dalam waktu dekat",
            "Gimana kalau kita jadwalkan waktu untuk ngobrol?",
            "Mau kita atur waktu untuk diskusi lebih lanjut?",
            "Bagaimana kalau kita bicarakan ini lebih serius?",
            "Kapan waktu yang tepat untuk diskusi mendalam?"
        ];

        // Risk reversal CTAs
        this.RISK_REVERSAL_CTAS = [
            "Kita bisa mulai dengan diskusi ringan dulu",
            "Tidak ada komitmen, cuma ngobrol-ngobrol aja",
            "Mau coba diskusi dulu tanpa ada tekanan?",
            "Gimana kalau kita mulai dengan konsultasi santai?",
            "Bisa mulai dengan sharing pengalaman dulu"
        ];

        // CTA combinations for maximum impact
        this.CTA_COMBINATIONS = {
            high_engagement: ['urgency', 'benefit_focused', 'authority'],
            medium_engagement: ['curiosity', 'social_proof', 'scarcity'],
            low_engagement: ['risk_reversal', 'fomo', 'benefit_focused'],
            skeptical: ['authority', 'social_proof', 'risk_reversal'],
            excited: ['urgency', 'scarcity', 'benefit_focused'],
            worried: ['risk_reversal', 'authority', 'social_proof']
        };
    }

    /**
     * Generate strategic CTA based on conversation context
     */
    generateStrategicCTA(context) {
        const {
            engagementLevel = 50,
            emotionalState = 'neutral',
            conversionReadiness = 50,
            objectionCount = 0,
            stage = 'initial'
        } = context;

        // Determine CTA strategy based on context
        let strategy = this.determineCTAStrategy(engagementLevel, emotionalState, conversionReadiness, objectionCount);
        
        // Get appropriate CTA types for this strategy
        let ctaTypes = this.CTA_COMBINATIONS[strategy] || ['benefit_focused', 'curiosity', 'urgency'];
        
        // Generate multiple CTAs for maximum impact
        let ctas = [];
        
        ctaTypes.forEach(type => {
            const cta = this.getCTAByType(type);
            if (cta) ctas.push(cta);
        });

        // Combine CTAs strategically
        return this.combineCTAs(ctas, strategy, conversionReadiness);
    }

    /**
     * Determine CTA strategy based on conversation context
     */
    determineCTAStrategy(engagementLevel, emotionalState, conversionReadiness, objectionCount) {
        // High conversion readiness - use urgency and scarcity
        if (conversionReadiness > 80) return 'excited';
        
        // High engagement but medium readiness - build momentum
        if (engagementLevel > 70 && conversionReadiness > 50) return 'high_engagement';
        
        // Skeptical or many objections - use authority and social proof
        if (emotionalState === 'skeptical' || objectionCount > 2) return 'skeptical';
        
        // Worried or concerned - use risk reversal
        if (emotionalState === 'worried' || emotionalState === 'frustrated') return 'worried';
        
        // Medium engagement - use curiosity and social proof
        if (engagementLevel > 40) return 'medium_engagement';
        
        // Low engagement - use risk reversal and FOMO
        return 'low_engagement';
    }

    /**
     * Get CTA by specific type
     */
    getCTAByType(type) {
        const ctaArrays = {
            urgency: this.URGENCY_CTAS,
            curiosity: this.CURIOSITY_CTAS,
            benefit_focused: this.BENEFIT_CTAS,
            fomo: this.FOMO_CTAS,
            social_proof: this.SOCIAL_PROOF_CTAS,
            authority: this.AUTHORITY_CTAS,
            scarcity: this.SCARCITY_CTAS,
            risk_reversal: this.RISK_REVERSAL_CTAS
        };

        const ctas = ctaArrays[type];
        if (!ctas || ctas.length === 0) return null;

        return ctas[Math.floor(Math.random() * ctas.length)];
    }

    /**
     * Combine multiple CTAs strategically
     */
    combineCTAs(ctas, strategy, conversionReadiness) {
        if (ctas.length === 0) {
            return `\n\n💬 Ketik 'admin' untuk konsultasi langsung`;
        }

        let combined = '';

        // High readiness - use assumptive approach
        if (conversionReadiness > 80) {
            combined += `\n\n${ctas[0]}`;
            if (ctas[1]) combined += `\n${ctas[1]}`;
        }
        // Medium readiness - use alternative approach
        else if (conversionReadiness > 50) {
            combined += `\n\n${ctas[0]}`;
            if (ctas[1]) combined += `\n\nAtau... ${ctas[1]}`;
        }
        // Low readiness - use curiosity and value
        else {
            combined += `\n\n${ctas[0]}`;
            if (ctas[1]) combined += `\n\n${ctas[1]}`;
            combined += `\n\n💼 Ketik 'admin' untuk bicara langsung`;
        }

        return combined;
    }

    /**
     * Generate follow-up CTA for silent prospects
     */
    generateFollowUpCTA(daysSilent) {
        let cta;

        if (daysSilent <= 1) {
            cta = this.getCTAByType('curiosity');
        } else if (daysSilent <= 3) {
            cta = this.getCTAByType('benefit_focused');
        } else if (daysSilent <= 7) {
            cta = this.getCTAByType('fomo');
        } else {
            cta = this.getCTAByType('risk_reversal');
        }

        return `\n\n${cta}\n\n💭 Atau ketik 'admin' kalau mau chat langsung`;
    }

    /**
     * Generate CTA for specific business scenarios
     */
    generateScenarioBasedCTA(scenario) {
        const scenarioCTAs = {
            first_contact: this.getCTAByType('curiosity'),
            showed_interest: this.getCTAByType('benefit_focused'),
            asked_questions: this.getCTAByType('authority'),
            mentioned_budget: this.getCTAByType('risk_reversal'),
            mentioned_timeline: this.getCTAByType('urgency'),
            compared_options: this.getCTAByType('social_proof'),
            expressed_doubt: this.getCTAByType('authority'),
            ready_to_buy: this.getCTAByType('scarcity')
        };

        const cta = scenarioCTAs[scenario] || this.getCTAByType('benefit_focused');

        return `\n\n${cta}`;
    }

    /**
     * A/B test different CTA approaches
     */
    getABTestCTA(testGroup = 'A') {
        if (testGroup === 'A') {
            // Direct approach
            return this.generateStrategicCTA({
                engagementLevel: 60,
                emotionalState: 'neutral',
                conversionReadiness: 50
            });
        } else {
            // Soft approach
            return this.generateStrategicCTA({
                engagementLevel: 40,
                emotionalState: 'worried',
                conversionReadiness: 30
            });
        }
    }

    /**
     * Generate emergency re-engagement CTA for lost prospects
     */
    generateEmergencyReEngagementCTA() {
        const emergencyCTAs = [
            "Mungkin ada hal yang terlewat dalam diskusi kita sebelumnya",
            "Ada update yang mungkin menarik untuk situasi Anda",
            "Gimana kalau kita coba pendekatan yang berbeda?",
            "Mungkin ada solusi lain yang lebih cocok untuk Anda",
            "Ada hal baru yang bisa kita eksplorasi bersama"
        ];

        const cta = emergencyCTAs[Math.floor(Math.random() * emergencyCTAs.length)];

        return `\n\n${cta}\n\n💬 Ketik 'admin' kalau mau ngobrol lagi`;
    }
}

module.exports = AdvancedCTAEngine;
