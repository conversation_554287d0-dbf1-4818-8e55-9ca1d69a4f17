{"name": "kawan", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "setup": "node setup.js", "start": "node src/index.js", "dev": "nodemon src/index.js"}, "author": "rio", "license": "ISC", "dependencies": {"axios": "^1.5.1", "body-parser": "^1.20.2", "cheerio": "^1.0.0-rc.12", "compromise": "^14.10.1", "dotenv": "^16.3.1", "fs": "^0.0.1-security", "node-schedule": "^2.1.1", "nodemon": "^3.0.1", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "qrcode-terminal": "^0.12.0", "uuid": "^9.0.1", "whatsapp-web.js": "^1.31.0", "xlsx": "^0.18.5"}}