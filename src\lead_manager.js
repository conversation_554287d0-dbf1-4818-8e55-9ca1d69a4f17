const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

class LeadManager {
    constructor() {
        this.leadsFile = process.env.LEADS_FILE || 'leads.json';
        this.leads = [];
        this.loadLeads();
    }

    /**
     * Load leads from JSON file
     */
    loadLeads() {
        try {
            if (fs.existsSync(this.leadsFile)) {
                const data = fs.readFileSync(this.leadsFile, 'utf8');
                this.leads = JSON.parse(data);
            } else {
                this.leads = [];
                this.saveLeads();
            }
        } catch (error) {
            console.error('Error loading leads:', error);
            this.leads = [];
        }
    }

    /**
     * Save leads to JSON file
     */
    saveLeads() {
        try {
            fs.writeFileSync(this.leadsFile, JSON.stringify(this.leads, null, 2));
        } catch (error) {
            console.error('Error saving leads:', error);
        }
    }

    /**
     * Add a new lead
     */
    addLead(businessName, phoneNumber, message, source = 'whatsapp_blast') {
        const lead = {
            id: uuidv4(),
            businessName: businessName,
            phoneNumber: phoneNumber,
            message: message,
            source: source,
            status: 'new',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            followUpDate: null,
            notes: []
        };

        this.leads.push(lead);
        this.saveLeads();
        
        console.log(`New lead added: ${businessName} (${phoneNumber})`);
        return lead;
    }

    /**
     * Update lead status
     */
    updateLeadStatus(leadId, status, notes = null) {
        const lead = this.leads.find(l => l.id === leadId);
        if (lead) {
            lead.status = status;
            lead.updatedAt = new Date().toISOString();
            
            if (notes) {
                lead.notes.push({
                    note: notes,
                    timestamp: new Date().toISOString()
                });
            }
            
            this.saveLeads();
            return lead;
        }
        return null;
    }

    /**
     * Find lead by phone number
     */
    findLeadByPhone(phoneNumber) {
        return this.leads.find(l => l.phoneNumber === phoneNumber);
    }

    /**
     * Get all leads
     */
    getAllLeads() {
        return this.leads;
    }

    /**
     * Get leads by status
     */
    getLeadsByStatus(status) {
        return this.leads.filter(l => l.status === status);
    }

    /**
     * Get new leads
     */
    getNewLeads() {
        return this.getLeadsByStatus('new');
    }

    /**
     * Get interested leads
     */
    getInterestedLeads() {
        return this.getLeadsByStatus('interested');
    }

    /**
     * Get qualified leads
     */
    getQualifiedLeads() {
        return this.getLeadsByStatus('qualified');
    }

    /**
     * Set follow-up date for a lead
     */
    setFollowUpDate(leadId, followUpDate) {
        const lead = this.leads.find(l => l.id === leadId);
        if (lead) {
            lead.followUpDate = followUpDate;
            lead.updatedAt = new Date().toISOString();
            this.saveLeads();
            return lead;
        }
        return null;
    }

    /**
     * Add note to lead
     */
    addNoteToLead(leadId, note) {
        const lead = this.leads.find(l => l.id === leadId);
        if (lead) {
            lead.notes.push({
                note: note,
                timestamp: new Date().toISOString()
            });
            lead.updatedAt = new Date().toISOString();
            this.saveLeads();
            return lead;
        }
        return null;
    }

    /**
     * Get leads that need follow-up
     */
    getLeadsNeedingFollowUp() {
        const today = new Date().toISOString().split('T')[0];
        return this.leads.filter(l => 
            l.followUpDate && 
            l.followUpDate <= today && 
            l.status !== 'closed' && 
            l.status !== 'not_interested'
        );
    }

    /**
     * Get lead statistics
     */
    getStatistics() {
        const total = this.leads.length;
        const newLeads = this.getNewLeads().length;
        const interested = this.getInterestedLeads().length;
        const qualified = this.getQualifiedLeads().length;
        const closed = this.getLeadsByStatus('closed').length;
        const notInterested = this.getLeadsByStatus('not_interested').length;

        return {
            total,
            new: newLeads,
            interested,
            qualified,
            closed,
            notInterested,
            conversionRate: total > 0 ? ((closed / total) * 100).toFixed(2) : 0
        };
    }

    /**
     * Export leads to CSV format
     */
    exportToCSV() {
        const headers = ['ID', 'Business Name', 'Phone Number', 'Status', 'Source', 'Created At', 'Updated At', 'Follow Up Date', 'Notes'];
        const csvData = [headers.join(',')];

        this.leads.forEach(lead => {
            const notes = lead.notes.map(n => n.note).join('; ');
            const row = [
                lead.id,
                `"${lead.businessName}"`,
                lead.phoneNumber,
                lead.status,
                lead.source,
                lead.createdAt,
                lead.updatedAt,
                lead.followUpDate || '',
                `"${notes}"`
            ];
            csvData.push(row.join(','));
        });

        return csvData.join('\n');
    }
}

module.exports = LeadManager;
