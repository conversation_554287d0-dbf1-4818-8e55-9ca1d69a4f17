const fs = require('fs');
const ConversationManager = require('./conversation_manager');
const LeadManager = require('./lead_manager');

class AnalyticsManager {
    constructor() {
        this.conversationManager = new ConversationManager();
        this.leadManager = new LeadManager();
        this.analyticsFile = 'analytics_data.json';
        this.loadAnalytics();
    }

    /**
     * Load analytics data from file
     */
    loadAnalytics() {
        try {
            if (fs.existsSync(this.analyticsFile)) {
                const data = fs.readFileSync(this.analyticsFile, 'utf8');
                this.analytics = JSON.parse(data);
            } else {
                this.analytics = {
                    dailyStats: {},
                    weeklyStats: {},
                    monthlyStats: {},
                    conversionFunnel: {},
                    responseEffectiveness: {},
                    painPointAnalysis: {},
                    lastUpdated: new Date().toISOString()
                };
                this.saveAnalytics();
            }
        } catch (error) {
            console.error('Error loading analytics:', error);
            this.analytics = {};
        }
    }

    /**
     * Save analytics data to file
     */
    saveAnalytics() {
        try {
            this.analytics.lastUpdated = new Date().toISOString();
            fs.writeFileSync(this.analyticsFile, JSON.stringify(this.analytics, null, 2));
        } catch (error) {
            console.error('Error saving analytics:', error);
        }
    }

    /**
     * Generate comprehensive analytics report
     */
    generateReport() {
        const conversations = this.conversationManager.conversations;
        const leads = this.leadManager.getAllLeads();
        
        const report = {
            overview: this.getOverviewMetrics(conversations, leads),
            conversionFunnel: this.getConversionFunnel(conversations),
            stageAnalysis: this.getStageAnalysis(conversations),
            painPointAnalysis: this.getPainPointAnalysis(conversations),
            timeToConversion: this.getTimeToConversionAnalysis(conversations),
            responseEffectiveness: this.getResponseEffectiveness(conversations),
            businessInsights: this.getBusinessInsights(conversations),
            recommendations: this.getRecommendations(conversations)
        };

        // Update analytics data
        this.updateAnalyticsData(report);
        
        return report;
    }

    /**
     * Get overview metrics
     */
    getOverviewMetrics(conversations, leads) {
        const conversationArray = Object.values(conversations);
        const totalConversations = conversationArray.length;
        const convertedConversations = conversationArray.filter(c => c.isConverted).length;
        const activeConversations = conversationArray.filter(c => !c.isConverted && 
            new Date() - new Date(c.lastActivity) < 24 * 60 * 60 * 1000).length;

        return {
            totalConversations,
            convertedConversations,
            activeConversations,
            conversionRate: totalConversations > 0 ? 
                ((convertedConversations / totalConversations) * 100).toFixed(2) : 0,
            totalLeads: leads.length,
            qualifiedLeads: leads.filter(l => l.status === 'qualified').length,
            avgMessagesPerConversation: totalConversations > 0 ? 
                (conversationArray.reduce((sum, c) => sum + c.messages.length, 0) / totalConversations).toFixed(1) : 0
        };
    }

    /**
     * Get conversion funnel analysis
     */
    getConversionFunnel(conversations) {
        const conversationArray = Object.values(conversations);
        const stages = ['initial', 'qualification', 'problem_discovery', 'solution_presentation', 'interest_building', 'deal_closure', 'converted'];
        
        const funnel = {};
        stages.forEach(stage => {
            funnel[stage] = conversationArray.filter(c => 
                c.stage === stage || (stage === 'converted' && c.isConverted)
            ).length;
        });

        // Calculate drop-off rates
        const dropOffRates = {};
        for (let i = 0; i < stages.length - 1; i++) {
            const current = funnel[stages[i]];
            const next = funnel[stages[i + 1]];
            dropOffRates[`${stages[i]}_to_${stages[i + 1]}`] = current > 0 ? 
                (((current - next) / current) * 100).toFixed(2) : 0;
        }

        return { funnel, dropOffRates };
    }

    /**
     * Get stage analysis
     */
    getStageAnalysis(conversations) {
        const conversationArray = Object.values(conversations);
        const stageStats = {};

        conversationArray.forEach(conv => {
            const stage = conv.stage;
            if (!stageStats[stage]) {
                stageStats[stage] = {
                    count: 0,
                    avgMessages: 0,
                    avgTimeSpent: 0,
                    conversionRate: 0
                };
            }
            stageStats[stage].count++;
        });

        // Calculate averages for each stage
        Object.keys(stageStats).forEach(stage => {
            const stageConversations = conversationArray.filter(c => c.stage === stage);
            const totalMessages = stageConversations.reduce((sum, c) => sum + c.messages.length, 0);
            const totalTime = stageConversations.reduce((sum, c) => {
                const start = new Date(c.startTime);
                const end = new Date(c.lastActivity);
                return sum + (end - start);
            }, 0);
            const converted = stageConversations.filter(c => c.isConverted).length;

            stageStats[stage].avgMessages = stageConversations.length > 0 ? 
                (totalMessages / stageConversations.length).toFixed(1) : 0;
            stageStats[stage].avgTimeSpent = stageConversations.length > 0 ? 
                Math.round(totalTime / stageConversations.length / (1000 * 60)) : 0; // in minutes
            stageStats[stage].conversionRate = stageConversations.length > 0 ? 
                ((converted / stageConversations.length) * 100).toFixed(2) : 0;
        });

        return stageStats;
    }

    /**
     * Get pain point analysis
     */
    getPainPointAnalysis(conversations) {
        const conversationArray = Object.values(conversations);
        const painPointCategories = {};
        const commonPainPoints = {};

        conversationArray.forEach(conv => {
            if (conv.problemCategory) {
                painPointCategories[conv.problemCategory] = 
                    (painPointCategories[conv.problemCategory] || 0) + 1;
            }

            conv.painPoints.forEach(painPoint => {
                const key = painPoint.toLowerCase().substring(0, 50); // First 50 chars as key
                commonPainPoints[key] = (commonPainPoints[key] || 0) + 1;
            });
        });

        // Sort by frequency
        const sortedPainPoints = Object.entries(commonPainPoints)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10); // Top 10

        return {
            categories: painPointCategories,
            topPainPoints: sortedPainPoints
        };
    }

    /**
     * Get time to conversion analysis
     */
    getTimeToConversionAnalysis(conversations) {
        const conversationArray = Object.values(conversations);
        const convertedConversations = conversationArray.filter(c => c.isConverted && c.conversionTime);

        if (convertedConversations.length === 0) {
            return { avgTimeToConversion: 0, conversionTimeDistribution: {} };
        }

        const conversionTimes = convertedConversations.map(conv => {
            const start = new Date(conv.startTime);
            const end = new Date(conv.conversionTime);
            return (end - start) / (1000 * 60); // in minutes
        });

        const avgTimeToConversion = conversionTimes.reduce((sum, time) => sum + time, 0) / conversionTimes.length;

        // Distribution buckets
        const distribution = {
            'under_30_min': conversionTimes.filter(t => t < 30).length,
            '30_60_min': conversionTimes.filter(t => t >= 30 && t < 60).length,
            '1_3_hours': conversionTimes.filter(t => t >= 60 && t < 180).length,
            '3_24_hours': conversionTimes.filter(t => t >= 180 && t < 1440).length,
            'over_24_hours': conversionTimes.filter(t => t >= 1440).length
        };

        return {
            avgTimeToConversion: Math.round(avgTimeToConversion),
            conversionTimeDistribution: distribution
        };
    }

    /**
     * Get response effectiveness analysis
     */
    getResponseEffectiveness(conversations) {
        const conversationArray = Object.values(conversations);
        const stageEffectiveness = {};

        conversationArray.forEach(conv => {
            conv.messages.forEach((msg, index) => {
                if (!msg.isFromUser && msg.stage) {
                    if (!stageEffectiveness[msg.stage]) {
                        stageEffectiveness[msg.stage] = {
                            totalResponses: 0,
                            engagementRate: 0,
                            progressionRate: 0
                        };
                    }
                    stageEffectiveness[msg.stage].totalResponses++;

                    // Check if user responded after this message
                    const nextUserMessage = conv.messages.slice(index + 1).find(m => m.isFromUser);
                    if (nextUserMessage) {
                        stageEffectiveness[msg.stage].engagementRate++;
                    }
                }
            });
        });

        // Calculate rates
        Object.keys(stageEffectiveness).forEach(stage => {
            const stats = stageEffectiveness[stage];
            stats.engagementRate = stats.totalResponses > 0 ? 
                ((stats.engagementRate / stats.totalResponses) * 100).toFixed(2) : 0;
        });

        return stageEffectiveness;
    }

    /**
     * Get business insights
     */
    getBusinessInsights(conversations) {
        const conversationArray = Object.values(conversations);
        
        return {
            peakHours: this.getPeakHours(conversationArray),
            mostEffectiveStage: this.getMostEffectiveStage(conversationArray),
            commonDropOffPoints: this.getCommonDropOffPoints(conversationArray),
            highValueProspects: this.getHighValueProspects(conversationArray)
        };
    }

    /**
     * Get recommendations based on data
     */
    getRecommendations(conversations) {
        const conversationArray = Object.values(conversations);
        const recommendations = [];

        // Analyze conversion rates by stage
        const stageAnalysis = this.getStageAnalysis(conversations);
        const lowestConversionStage = Object.entries(stageAnalysis)
            .sort(([,a], [,b]) => parseFloat(a.conversionRate) - parseFloat(b.conversionRate))[0];

        if (lowestConversionStage && parseFloat(lowestConversionStage[1].conversionRate) < 20) {
            recommendations.push({
                type: 'improvement',
                priority: 'high',
                message: `Improve conversion at ${lowestConversionStage[0]} stage (${lowestConversionStage[1].conversionRate}% conversion rate)`
            });
        }

        // Check average response time
        const avgMessages = conversationArray.reduce((sum, c) => sum + c.messages.length, 0) / conversationArray.length;
        if (avgMessages > 15) {
            recommendations.push({
                type: 'optimization',
                priority: 'medium',
                message: 'Consider shortening conversation flow - average of ' + avgMessages.toFixed(1) + ' messages per conversation'
            });
        }

        return recommendations;
    }

    /**
     * Helper methods for business insights
     */
    getPeakHours(conversations) {
        const hourCounts = {};
        conversations.forEach(conv => {
            const hour = new Date(conv.startTime).getHours();
            hourCounts[hour] = (hourCounts[hour] || 0) + 1;
        });
        return Object.entries(hourCounts).sort(([,a], [,b]) => b - a).slice(0, 3);
    }

    getMostEffectiveStage(conversations) {
        const stageConversions = {};
        conversations.forEach(conv => {
            if (conv.isConverted) {
                stageConversions[conv.stage] = (stageConversions[conv.stage] || 0) + 1;
            }
        });
        return Object.entries(stageConversions).sort(([,a], [,b]) => b - a)[0];
    }

    getCommonDropOffPoints(conversations) {
        const dropOffs = {};
        conversations.filter(c => !c.isConverted).forEach(conv => {
            dropOffs[conv.stage] = (dropOffs[conv.stage] || 0) + 1;
        });
        return Object.entries(dropOffs).sort(([,a], [,b]) => b - a).slice(0, 3);
    }

    getHighValueProspects(conversations) {
        return conversations
            .filter(c => c.conversionProbability > 70 && !c.isConverted)
            .sort((a, b) => b.conversionProbability - a.conversionProbability)
            .slice(0, 5);
    }

    /**
     * Update analytics data
     */
    updateAnalyticsData(report) {
        const today = new Date().toISOString().split('T')[0];
        this.analytics.dailyStats[today] = report.overview;
        this.saveAnalytics();
    }

    /**
     * Export comprehensive analytics to CSV
     */
    exportAnalyticsToCSV() {
        const report = this.generateReport();
        const csvSections = [];

        // Overview metrics
        csvSections.push('OVERVIEW METRICS');
        csvSections.push('Metric,Value');
        Object.entries(report.overview).forEach(([key, value]) => {
            csvSections.push(`${key},${value}`);
        });
        csvSections.push('');

        // Conversion funnel
        csvSections.push('CONVERSION FUNNEL');
        csvSections.push('Stage,Count');
        Object.entries(report.conversionFunnel.funnel).forEach(([stage, count]) => {
            csvSections.push(`${stage},${count}`);
        });
        csvSections.push('');

        // Pain point analysis
        csvSections.push('TOP PAIN POINTS');
        csvSections.push('Pain Point,Frequency');
        report.painPointAnalysis.topPainPoints.forEach(([painPoint, frequency]) => {
            csvSections.push(`"${painPoint}",${frequency}`);
        });

        return csvSections.join('\n');
    }
}

module.exports = AnalyticsManager;
