const { runAiAssistant } = require('./src/ai_assistant.js');

// Test the AI-first approach
async function testAIFirst() {
    console.log('🧪 Testing AI-First Approach...');
    console.log('=' .repeat(50));
    
    try {
        // Test 1: Initial greeting
        console.log('\n📝 Test 1: Initial Business Inquiry');
        const response1 = await runAiAssistant('Halo, saya butuh website untuk bisnis saya', 'test_user_1', 'Test User');
        console.log('Response:', response1);
        
        // Test 2: System inquiry
        console.log('\n📝 Test 2: System Inquiry');
        const response2 = await runAiAssistant('Saya butuh sistem inventory untuk toko saya', 'test_user_2', 'Test User 2');
        console.log('Response:', response2);
        
        // Test 3: Pricing inquiry
        console.log('\n📝 Test 3: Pricing Inquiry');
        const response3 = await runAiAssistant('Berapa harga untuk buat dashboard analytics?', 'test_user_3', 'Test User 3');
        console.log('Response:', response3);
        
        console.log('\n✅ All tests completed successfully!');
        console.log('🎯 AI-First approach is working correctly!');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.log('\n🔄 Fallback system should handle this gracefully.');
    }
}

// Run the test
testAIFirst();